// package com.hll.dataconvertservice.controller;
//
// import com.hll.dataconvertservice.utils.ArchiveUtils;
// import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
// import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
// import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.io.TempDir;
// import sun.reflect.generics.tree.VoidDescriptor;
//
// import java.io.*;
// import java.nio.file.Files;
// import java.nio.file.Path;
// import java.util.ArrayList;
// import java.util.List;
//
// import static org.junit.jupiter.api.Assertions.*;
//
// /**
//  * Test class for DataConvertController compression functionality
//  */
// public class DataConvertControllerTest {
//     @Test
//     public void testMeasureCountOfArchive() throws IOException {
//         System.out.println(ArchiveUtils.getArchiveFileCount("/Users/<USER>/Z01/MYS_ROAD_IHEX320_25Q2H2508_01Z01.tar.gz"));
//     }
//
//     @Test
//     public void testCompressFilesToTarGz(@TempDir Path tempDir) throws IOException {
//         // Create test files
//         List<File> testFiles = createTestFiles(tempDir);
//
//         // Create controller instance
//         DataConvertController controller = new DataConvertController();
//
//         // Define output tar.gz file
//         String tarGzFileName = tempDir.resolve("test_archive.tar.gz").toString();
//
//         // Use reflection to call the private method
//         try {
//             java.lang.reflect.Method method = DataConvertController.class.getDeclaredMethod(
//                 "compressFilesToTarGz", List.class, String.class);
//             method.setAccessible(true);
//             method.invoke(controller, testFiles, tarGzFileName);
//         } catch (Exception e) {
//             fail("Failed to invoke compressFilesToTarGz method: " + e.getMessage());
//         }
//
//         // Verify the tar.gz file was created
//         File tarGzFile = new File(tarGzFileName);
//         assertTrue(tarGzFile.exists(), "tar.gz file should be created");
//         assertTrue(tarGzFile.length() > 0, "tar.gz file should not be empty");
//
//         // Verify the contents of the tar.gz file
//         verifyTarGzContents(tarGzFile, testFiles);
//     }
//
//     @Test
//     public void testCompressFilesToTarGzWithEmptyList(@TempDir Path tempDir) {
//         DataConvertController controller = new DataConvertController();
//         String tarGzFileName = tempDir.resolve("empty_archive.tar.gz").toString();
//
//         try {
//             java.lang.reflect.Method method = DataConvertController.class.getDeclaredMethod(
//                 "compressFilesToTarGz", List.class, String.class);
//             method.setAccessible(true);
//
//             // Should throw IllegalArgumentException for empty list
//             assertThrows(java.lang.reflect.InvocationTargetException.class, () -> {
//                 method.invoke(controller, new ArrayList<File>(), tarGzFileName);
//             });
//         } catch (Exception e) {
//             fail("Failed to test empty list scenario: " + e.getMessage());
//         }
//     }
//
//     private List<File> createTestFiles(Path tempDir) throws IOException {
//         List<File> files = new ArrayList<>();
//
//         // Create test GeoJSON files similar to what the application would generate
//         String[] tableNames = {"link", "node", "relation", "rule"};
//
//         for (String tableName : tableNames) {
//             File file = tempDir.resolve(tableName + ".geojson").toFile();
//
//             // Write sample GeoJSON content
//             String geoJsonContent = String.format(
//                 "{\n" +
//                 "  \"type\": \"FeatureCollection\",\n" +
//                 "  \"features\": [\n" +
//                 "    {\n" +
//                 "      \"type\": \"Feature\",\n" +
//                 "      \"properties\": {\n" +
//                 "        \"table\": \"%s\",\n" +
//                 "        \"id\": 1\n" +
//                 "      },\n" +
//                 "      \"geometry\": {\n" +
//                 "        \"type\": \"Point\",\n" +
//                 "        \"coordinates\": [0, 0]\n" +
//                 "      }\n" +
//                 "    }\n" +
//                 "  ]\n" +
//                 "}", tableName);
//
//             Files.write(file.toPath(), geoJsonContent.getBytes());
//             files.add(file);
//         }
//
//         return files;
//     }
//
//     private void verifyTarGzContents(File tarGzFile, List<File> originalFiles) throws IOException {
//         try (FileInputStream fis = new FileInputStream(tarGzFile);
//              GzipCompressorInputStream gzis = new GzipCompressorInputStream(fis);
//              TarArchiveInputStream tais = new TarArchiveInputStream(gzis)) {
//
//             List<String> extractedFileNames = new ArrayList<>();
//             TarArchiveEntry entry;
//
//             while ((entry = tais.getNextTarEntry()) != null) {
//                 if (!entry.isDirectory()) {
//                     extractedFileNames.add(entry.getName());
//
//                     // Verify file size matches
//                     File originalFile = findFileByName(originalFiles, entry.getName());
//                     assertNotNull(originalFile, "Original file should exist: " + entry.getName());
//                     assertEquals(originalFile.length(), entry.getSize(),
//                         "File size should match for: " + entry.getName());
//                 }
//             }
//
//             // Verify all original files are in the archive
//             assertEquals(originalFiles.size(), extractedFileNames.size(),
//                 "Number of files in archive should match original files");
//
//             for (File originalFile : originalFiles) {
//                 assertTrue(extractedFileNames.contains(originalFile.getName()),
//                     "Archive should contain file: " + originalFile.getName());
//             }
//         }
//     }
//
//     private File findFileByName(List<File> files, String name) {
//         return files.stream()
//             .filter(file -> file.getName().equals(name))
//             .findFirst()
//             .orElse(null);
//     }
// }
