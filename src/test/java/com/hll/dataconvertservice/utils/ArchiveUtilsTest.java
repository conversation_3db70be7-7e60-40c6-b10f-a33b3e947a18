package com.hll.dataconvertservice.utils;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * Test class for ArchiveUtils
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ArchiveUtilsTest {

    private Path tempDir;
    private List<File> testFiles;

    @Before
    public void setUp() throws IOException {
        // Create temporary directory for testing
        tempDir = Files.createTempDirectory("archive-utils-test");
        testFiles = new ArrayList<>();

        // Create test files
        for (int i = 1; i <= 3; i++) {
            File testFile = tempDir.resolve("test" + i + ".txt").toFile();
            try (FileWriter writer = new FileWriter(testFile)) {
                writer.write("This is test file " + i + "\nWith some content for testing.");
            }
            testFiles.add(testFile);
        }

        // Create a subdirectory with files for directory compression tests
        Path subDir = tempDir.resolve("subdir");
        Files.createDirectories(subDir);
        File subFile = subDir.resolve("subfile.txt").toFile();
        try (FileWriter writer = new FileWriter(subFile)) {
            writer.write("This is a file in subdirectory.");
        }
    }

    @After
    public void tearDown() throws IOException {
        // Clean up temporary files and directories
        deleteDirectory(tempDir.toFile());
    }

    @Test
    public void testCompressFilesToTarGz() throws IOException {
        String archivePath = tempDir.resolve("test.tar.gz").toString();
        
        ArchiveUtils.compressFilesToTarGz(testFiles, archivePath);
        
        File archiveFile = new File(archivePath);
        assertTrue("Archive file should exist", archiveFile.exists());
        assertTrue("Archive file should not be empty", archiveFile.length() > 0);
        
        // Test file count
        int fileCount = ArchiveUtils.getArchiveFileCount(archivePath);
        assertEquals("Archive should contain 3 files", 3, fileCount);
    }

    @Test
    public void testCompressFilesToTar() throws IOException {
        String archivePath = tempDir.resolve("test.tar").toString();
        
        ArchiveUtils.compressFiles(testFiles, archivePath, ArchiveUtils.ArchiveFormat.TAR);
        
        File archiveFile = new File(archivePath);
        assertTrue("Archive file should exist", archiveFile.exists());
        assertTrue("Archive file should not be empty", archiveFile.length() > 0);
        
        // Test file count
        int fileCount = ArchiveUtils.getArchiveFileCount(archivePath);
        assertEquals("Archive should contain 3 files", 3, fileCount);
    }

    @Test
    public void testCompressFilesToZip() throws IOException {
        String archivePath = tempDir.resolve("test.zip").toString();
        
        ArchiveUtils.compressFiles(testFiles, archivePath, ArchiveUtils.ArchiveFormat.ZIP);
        
        File archiveFile = new File(archivePath);
        assertTrue("Archive file should exist", archiveFile.exists());
        assertTrue("Archive file should not be empty", archiveFile.length() > 0);
        
        // Test file count
        int fileCount = ArchiveUtils.getArchiveFileCount(archivePath);
        assertEquals("Archive should contain 3 files", 3, fileCount);
    }

    @Test
    public void testCompressDirectory() throws IOException {
        String archivePath = tempDir.resolve("directory.tar.gz").toString();
        
        ArchiveUtils.compressDirectory(tempDir.toString(), archivePath, ArchiveUtils.ArchiveFormat.TAR_GZ);
        
        File archiveFile = new File(archivePath);
        assertTrue("Archive file should exist", archiveFile.exists());
        assertTrue("Archive file should not be empty", archiveFile.length() > 0);
        
        // Should contain files and directories
        int entryCount = ArchiveUtils.getArchiveFileCount(archivePath);
        assertTrue("Archive should contain multiple entries", entryCount > 3);
    }

    @Test
    public void testExtractTarGz() throws IOException {
        // First create an archive
        String archivePath = tempDir.resolve("extract-test.tar.gz").toString();
        ArchiveUtils.compressFilesToTarGz(testFiles, archivePath);
        
        // Create extraction directory
        Path extractDir = tempDir.resolve("extracted");
        Files.createDirectories(extractDir);
        
        // Extract the archive
        ArchiveUtils.extractArchive(archivePath, extractDir.toString());
        
        // Verify extracted files
        for (File originalFile : testFiles) {
            File extractedFile = extractDir.resolve(originalFile.getName()).toFile();
            assertTrue("Extracted file should exist: " + originalFile.getName(), extractedFile.exists());
            assertEquals("File sizes should match", originalFile.length(), extractedFile.length());
        }
    }

    @Test
    public void testExtractZip() throws IOException {
        // First create a ZIP archive
        String archivePath = tempDir.resolve("extract-test.zip").toString();
        ArchiveUtils.compressFiles(testFiles, archivePath, ArchiveUtils.ArchiveFormat.ZIP);
        
        // Create extraction directory
        Path extractDir = tempDir.resolve("extracted-zip");
        Files.createDirectories(extractDir);
        
        // Extract the archive
        ArchiveUtils.extractArchive(archivePath, extractDir.toString());
        
        // Verify extracted files
        for (File originalFile : testFiles) {
            File extractedFile = extractDir.resolve(originalFile.getName()).toFile();
            assertTrue("Extracted file should exist: " + originalFile.getName(), extractedFile.exists());
            assertEquals("File sizes should match", originalFile.length(), extractedFile.length());
        }
    }

    @Test
    public void testArchiveFormatDetection() {
        assertTrue("Should support .tar.gz files", ArchiveUtils.isSupportedArchive("test.tar.gz"));
        assertTrue("Should support .tar files", ArchiveUtils.isSupportedArchive("test.tar"));
        assertTrue("Should support .zip files", ArchiveUtils.isSupportedArchive("test.zip"));
        assertTrue("Should support .tgz files", ArchiveUtils.isSupportedArchive("test.tgz"));
        assertFalse("Should not support .rar files", ArchiveUtils.isSupportedArchive("test.rar"));
        assertFalse("Should not support .7z files", ArchiveUtils.isSupportedArchive("test.7z"));
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCompressEmptyFileList() throws IOException {
        List<File> emptyList = new ArrayList<>();
        ArchiveUtils.compressFilesToTarGz(emptyList, "test.tar.gz");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testCompressNullFileList() throws IOException {
        ArchiveUtils.compressFilesToTarGz(null, "test.tar.gz");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testExtractNonExistentArchive() throws IOException {
        ArchiveUtils.extractArchive("non-existent.tar.gz", tempDir.toString());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testUnsupportedArchiveFormat() throws IOException {
        ArchiveUtils.ArchiveFormat.fromFileName("test.rar");
    }

    /**
     * Utility method to recursively delete a directory
     */
    private void deleteDirectory(File directory) {
        if (directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
