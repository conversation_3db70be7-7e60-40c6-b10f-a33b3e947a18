// package com.hll.dataconvertservice;
//
// import com.hll.dataconvertservice.service.S3Service;
// import org.junit.jupiter.api.Test;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.TestPropertySource;
//
// import javax.annotation.Resource;
//
// /**
//  * 增量数据转换功能测试
//  *
//  * 测试新增的incrementalDataConvert2GeoJSON方法的功能
//  */
// @SpringBootTest
// // @TestPropertySource(locations = "classpath:application-test.properties")
// public class IncrementalDataConvertTest {
//
//     @Resource
//     private S3Service s3Service;
//
//     @Test
//     public void testUploadS3() {
//         boolean res = s3Service.uploadFileToS3("/Users/<USER>/link_incre.geojson", "test/link_incre.geojson");
//         System.out.println(res);
//     }
//
//
//     /**
//      * 测试增量数据转换功能
//      *
//      * 注意：此测试需要实际的数据库连接，请根据实际环境配置数据库参数
//      */
//     @Test
//     public void testIncrementalDataConvert() {
//         // 这是一个示例测试，展示如何使用新的API
//
//         // 示例参数
//         String url = "localhost";
//         Integer port = 5432;
//         String dbName = "test_db";
//         String dbUser = "postgres";
//         String dbPwd = "password";
//         String geojsonpath = "/tmp/test_output.geojson";
//         String startDate = "2023-01-01 00:00:00";
//         String endDate = "2023-12-31 23:59:59";
//         String[] tableNames = {"link_m", "node_m", "relation_m", "rule_m"};
//         String[] excludeFields = {"internal_id", "temp_field"};
//
//         System.out.println("增量数据转换测试参数:");
//         System.out.println("数据库: " + url + ":" + port + "/" + dbName);
//         System.out.println("日期范围: " + startDate + " 到 " + endDate);
//         System.out.println("处理表: " + String.join(", ", tableNames));
//         System.out.println("排除字段: " + String.join(", ", excludeFields));
//         System.out.println("输出路径: " + geojsonpath);
//
//         // 实际测试需要真实的数据库连接
//         // 这里只是展示API的使用方式
//         System.out.println("\n注意：实际测试需要配置真实的数据库连接参数");
//     }
//
//     /**
//      * 测试link_m表的字段映射功能
//      */
//     @Test
//     public void testLinkMFieldMapping() {
//         System.out.println("link_m表字段映射规则:");
//         System.out.println("link_id -> id");
//         System.out.println("dir -> direction");
//         System.out.println("app -> constSt");
//         System.out.println("devs -> detailcity");
//         System.out.println("spet -> special");
//         System.out.println("funct -> funcclass");
//         System.out.println("urban -> uflag");
//         System.out.println("pave -> roadCond");
//         System.out.println("lane_n -> lanenumsum");
//         System.out.println("lane_l -> lanenums2e");
//         System.out.println("lane_r -> lanenume2s");
//         System.out.println("lane_c -> lanenumc");
//         System.out.println("viad -> elevated");
//         System.out.println("l_admin -> admincodel");
//         System.out.println("r_admin -> admincoder");
//         System.out.println("f_speed -> spdlmts2e");
//         System.out.println("t_speed -> spdlmte2s");
//         System.out.println("sp_class -> speedclass");
//         System.out.println("dici_type -> dcType");
//         System.out.println("t_admin -> tAdmin");
//         System.out.println("time_zone -> timeZone");
//         System.out.println("\n增强功能：所有其他字段将直接复制，确保无数据丢失");
//     }
//
//     /**
//      * 测试node_m表的字段映射功能
//      */
//     @Test
//     public void testNodeMFieldMapping() {
//         System.out.println("node_m表字段映射规则:");
//         System.out.println("node_id -> id");
//         System.out.println("light -> light_flag");
//         System.out.println("其他字段 -> 直接复制");
//     }
//
//     /**
//      * 测试relation_m和rule_m表的处理逻辑
//      */
//     @Test
//     public void testRelationAndRuleMapping() {
//         System.out.println("relation_m和rule_m表处理逻辑:");
//         System.out.println("- 过滤掉工作相关字段");
//         System.out.println("- 应用up_date字段的日期过滤");
//         System.out.println("- 直接复制其他所有字段");
//     }
// }
