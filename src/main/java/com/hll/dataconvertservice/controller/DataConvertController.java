package com.hll.dataconvertservice.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;

import com.hll.dataconvertservice.utils.ArchiveUtils;

import cn.hutool.core.util.StrUtil;
import com.hll.dataconvertservice.common.AjaxResult;
import com.hll.dataconvertservice.common.TableNameEnum;
import com.hll.dataconvertservice.gts4vect.Geotools;
import com.hll.dataconvertservice.service.S3Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.postgis.PostgisNGDataStoreFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class DataConvertController {
    @Value("${pg.url}")
    private String pgUrl;
    @Value("${pg.port}")
    private Integer pgPort;
    @Value("${pg.user}")
    private String pgUser;
    @Value("${pg.pwd}")
    private String pgPwd;


    @Autowired
    private S3Service s3Service;

    @PostMapping(value = {"/pg2shp"})
    public AjaxResult pg2shp(@RequestParam String url, @RequestParam Integer port, @RequestParam String dbName, @RequestParam String dbUser, @RequestParam String dbPwd, @RequestParam String shpFilePath, @RequestParam String pgTableName, @RequestParam String geomField) {
        boolean res;
        TimeInterval timer = DateUtil.timer();
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put(PostgisNGDataStoreFactory.DBTYPE.key, "postgis");
        params.put(PostgisNGDataStoreFactory.HOST.key, url);
        params.put(PostgisNGDataStoreFactory.PORT.key, port);
        params.put(PostgisNGDataStoreFactory.DATABASE.key, dbName);
        params.put(PostgisNGDataStoreFactory.SCHEMA.key, "public");
        params.put(PostgisNGDataStoreFactory.USER.key, dbUser);
        params.put(PostgisNGDataStoreFactory.PASSWD.key, dbPwd);
        try {
            DataStore dataStore = DataStoreFinder.getDataStore(params);
            Geotools geotools = new Geotools(dataStore);
            res = geotools.pgtable2shp(pgTableName, shpFilePath, geomField);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("shp文件导出失败，异常" + e.getMessage());
        }
        if (!res) {
            return AjaxResult.error("shp文件导出失败，DataStore为null");
        }
        return AjaxResult.success("shp文件导出成功！导出用时 " + timer.intervalSecond() + "s");
    }

    /**
     * shp数据入库
     *
     * @param url
     * @param port
     * @param dbName
     * @param dbUser
     * @param dbPwd
     * @param shpFilePath
     * @param pgTableName
     * @return
     * @Caution: 注意主键为fid，需改为gid
     */
    @PostMapping(value = {"/shp2db"})
    public AjaxResult shp2db(@RequestParam String url, @RequestParam Integer port, @RequestParam String dbName, @RequestParam String dbUser, @RequestParam String dbPwd, @RequestParam String shpFilePath, @RequestParam String pgTableName) {
        boolean res;
        TimeInterval timer = DateUtil.timer();
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put(PostgisNGDataStoreFactory.DBTYPE.key, "postgis");
        params.put(PostgisNGDataStoreFactory.HOST.key, url);
        params.put(PostgisNGDataStoreFactory.PORT.key, port);
        params.put(PostgisNGDataStoreFactory.DATABASE.key, dbName);
        params.put(PostgisNGDataStoreFactory.SCHEMA.key, "public");
        params.put(PostgisNGDataStoreFactory.USER.key, dbUser);
        params.put(PostgisNGDataStoreFactory.PASSWD.key, dbPwd);
        try {
            DataStore dataStore = DataStoreFinder.getDataStore(params);
            Geotools geotools = new Geotools(dataStore);
            res = geotools.shp2pgInjector(params, shpFilePath, pgTableName, Charset.forName("utf-8"));
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("shp文件入库失败，异常" + e.getMessage());
        }
        if (!res) {
            return AjaxResult.error("shp文件入库失败，DataStore为null");
        }
        log.info("shp文件入库成功！入库用时 " + timer.intervalSecond() + "s" + "表名：" + pgTableName);
        return AjaxResult.success("shp文件入库成功！入库用时 " + timer.intervalSecond() + "s");
    }

    @PostMapping(value = {"/pgtable2geojson"})
    public AjaxResult pgtable2geojson(@RequestParam String url, @RequestParam Integer port, @RequestParam String dbName,
                                      @RequestParam String dbUser, @RequestParam String dbPwd, @RequestParam String pIdName,
                                      @RequestParam String pgTableName, @RequestParam String geojsonpath, @RequestParam(required = false) String[] excludeFields) {
        boolean res;
        TimeInterval timer = DateUtil.timer();
        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put(PostgisNGDataStoreFactory.DBTYPE.key, "postgis");
        params.put(PostgisNGDataStoreFactory.HOST.key, url);
        params.put(PostgisNGDataStoreFactory.PORT.key, port);
        params.put(PostgisNGDataStoreFactory.DATABASE.key, dbName);
        params.put(PostgisNGDataStoreFactory.SCHEMA.key, "public");
        params.put(PostgisNGDataStoreFactory.USER.key, dbUser);
        params.put(PostgisNGDataStoreFactory.PASSWD.key, dbPwd);
        try {
            DataStore dataStore = DataStoreFinder.getDataStore(params);
            // geometry不换行的处理
            Geotools geotools = new Geotools(dataStore);
            // geometry换行的处理
            //  Geotools2 geotools = new Geotools2(dataStore);
            res = geotools.pgtable2geojson(pgTableName, geojsonpath, pIdName, excludeFields);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("geojson导出失败，异常" + e.getMessage());
        }
        if (!res) {
            return AjaxResult.error("geojson导出失败，DataStore为null");
        }
        log.info("geojson导出成功！导出用时{}s,导出库名：{}，导出表名：{}，导出路径：{} ", timer.intervalSecond(), dbName, pgTableName, geojsonpath);
        return AjaxResult.success("geojson导出成功！导出用时{" + timer.intervalSecond() + "}s,导出库名：{" + dbName + "}，导出表名：{" + pgTableName + "}，导出路径：{" + geojsonpath + "}");
    }

    /**
     * 增量数据转换并导出为GeoJSON格式
     * 支持基于up_date字段的日期过滤和数据转换规则
     *
     * @param url           数据库连接URL
     * @param port          数据库端口
     * @param dbName        数据库名称
     * @param dbUser        数据库用户名
     * @param dbPwd         数据库密码
     * @param geojsonPath   GeoJSON文件输出路径 eg:/home/<USER>/oversea/here/output/2025/geojson/q3/mys/release/road/Z01
     * @param startDate     开始日期 (格式: yyyy-MM-dd HH:mm:ss)
     * @param endDate       结束日期 (格式: yyyy-MM-dd HH:mm:ss)
     * @param tableNames    要处理的表名数组，支持: link_e, node_e, relation_e, rule_e
     * @param excludeFields 要排除的字段数组（可选）
     * @return 处理结果
     */
    @PostMapping(value = {"/incrementalDataConvert2GeoJSON"})
    public AjaxResult incrementalDataConvert2GeoJSON(
            @RequestParam(required = false) String url,
            @RequestParam(required = false) Integer port,
            @RequestParam String dbName,
            @RequestParam(required = false) String dbUser,
            @RequestParam(required = false) String dbPwd,
            @RequestParam String geojsonPath,
            @RequestParam String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam String compressNamePrefix,
            @RequestParam String s3KeyPrefix,
            @RequestParam String[] tableNames,
            @RequestParam(required = false) String[] excludeFields) {
        // geojsonpath: /home/<USER>/oversea/here/output/2025/geojson/q3/mys/release/road/Z01
        // fileName prefix: MYS_ROAD_IHEX320_25Q2H2508_01Z01.tar
        // fileName prefix: MYS_ROAD_IHEX320_25Q2H2508_01Z01.csv

        boolean res;
        TimeInterval timer = DateUtil.timer();

        if (StrUtil.isBlank(url)) url = pgUrl;
        if (port == null) port = pgPort;
        if (StrUtil.isBlank(dbUser)) dbUser = pgUser;
        if (StrUtil.isBlank(dbPwd)) dbPwd = pgPwd;
        if (StrUtil.isBlank(endDate)) endDate = DateUtil.format(DateUtil.endOfDay(new Date()), "yyyy-MM-dd HH:mm:ss");

        HashMap<String, Object> params = new HashMap<String, Object>();
        params.put(PostgisNGDataStoreFactory.DBTYPE.key, "postgis");
        params.put(PostgisNGDataStoreFactory.HOST.key, url);
        params.put(PostgisNGDataStoreFactory.PORT.key, port);
        params.put(PostgisNGDataStoreFactory.DATABASE.key, dbName);
        params.put(PostgisNGDataStoreFactory.SCHEMA.key, "public");
        params.put(PostgisNGDataStoreFactory.USER.key, dbUser);
        params.put(PostgisNGDataStoreFactory.PASSWD.key, dbPwd);

        try {
            DataStore dataStore = DataStoreFinder.getDataStore(params);
            Geotools geotools = new Geotools(dataStore);

            // 调用新的增量数据转换方法
            res = geotools.incrementalDataConvert2GeoJSON(
                    tableNames, geojsonPath, compressNamePrefix, startDate, endDate, excludeFields);

        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("增量数据转换导出失败，异常: " + e.getMessage());
        }

        if (!res) {
            return AjaxResult.error("增量数据转换导出失败，DataStore为null或处理失败");
        }
        // 4要素geojson导出成功，csv也导出成功，压缩4要素geojson为 geojsonPath + compressNamePrefix.tar.gz
        // 4个文件合并压缩： /geojsonPath
        List<File> exportFileList = new ArrayList<>();
        if (geojsonPath.endsWith("/")) {
            for (String tableName : tableNames) {
                File geojsonFile = new File(geojsonPath + TableNameEnum.getTargetTableName(tableName) + ".geojson");
                if (!geojsonFile.exists()) {
                    log.error(geojsonPath + TableNameEnum.getTargetTableName(tableName) + ".geojson not exist!!!");
                } else {
                    exportFileList.add(geojsonFile);
                }
            }
        } else {
            for (String tableName : tableNames) {
                File geojsonFile = new File(geojsonPath + "/" + TableNameEnum.getTargetTableName(tableName) + ".geojson");
                if (!geojsonFile.exists()) {
                    log.error(geojsonPath + "/" + TableNameEnum.getTargetTableName(tableName) + ".geojson not exist!!!");
                } else {
                    exportFileList.add(geojsonFile);
                }
            }
        }

        // Compress all files in exportFileList into tar.gz archive
        String tarGzFileName = "";
        if (!exportFileList.isEmpty()) {
            tarGzFileName = (geojsonPath.endsWith("/") ? geojsonPath : geojsonPath + "/") + compressNamePrefix + ".tar.gz";
            try {
                ArchiveUtils.compressFilesToTarGz(exportFileList, tarGzFileName);
                log.info("成功压缩 {} 个文件到: {}", exportFileList.size(), tarGzFileName);
            } catch (IOException e) {
                log.error("压缩文件失败: {}", e.getMessage(), e);
                return AjaxResult.error("压缩文件失败: " + e.getMessage());
            }
        } else {
            log.warn("没有找到需要压缩的GeoJSON文件");
        }

        // S3 upload functionality - only execute when GeoJSON conversion is successful
        boolean targzUploadFlag = false;
        boolean csvUploadFlag = false;
        String csvFileName = "";
        try {
            log.info("开始上传文件到S3，路径: {}, S3前缀: {}", geojsonPath, s3KeyPrefix);

            if (new File(tarGzFileName).exists()) {
                targzUploadFlag = s3Service.uploadFileToS3(tarGzFileName, s3KeyPrefix);
            } else {
                log.warn("S3上传完成，但没有tar.gz文件被上传。请检查目录: {}", geojsonPath);
                return AjaxResult.error("S3上传完成，但没有tar.gz文件被上传。请检查目录: {}", geojsonPath);
            }

            csvFileName = (geojsonPath.endsWith("/") ? geojsonPath : geojsonPath + "/") + compressNamePrefix + ".csv";

            if (new File(csvFileName).exists()) {
                csvUploadFlag = s3Service.uploadFileToS3(csvFileName, s3KeyPrefix);
            } else {
                log.warn("S3上传完成，但没有csv文件被上传。请检查目录: {}", geojsonPath);
                return AjaxResult.error("S3上传完成，但没有csv文件被上传。请检查目录: {}", geojsonPath);
            }

        } catch (Exception e) {
            log.error("S3上传过程中发生异常: {}", e.getMessage(), e);
            // Note: S3 upload failure doesn't affect the overall success of GeoJSON conversion
            return AjaxResult.error("S3上传过程中发生异常: {}", e.getMessage());
        }
        String resMsg;
        if (targzUploadFlag && csvUploadFlag) {
            long endTime = timer.intervalSecond();
            resMsg = "增量数据转换导出成功！导出+压缩+上传用时{" + endTime +
                    "}s,导出库名：{" + dbName + "}，导出表名：{" + String.join(",", tableNames) +
                    "}，导出路径：{" + geojsonPath + "},飞鸽上传文件：{"+tarGzFileName+","+csvFileName+"}";


            log.info("增量数据转换导出成功！导出+压缩+上传用时{}s,导出库名：{}，导出表名：{}，导出路径：{}，飞鸽上传文件：{} ",
                    endTime, dbName, String.join(",", tableNames), geojsonPath, tarGzFileName + "," + csvFileName);
            return AjaxResult.success(resMsg);
        } else {
            resMsg = "本次增量出品，失败，请检查日志!";
            return AjaxResult.error(resMsg);
        }
    }


    @GetMapping("/upload_single_file_2_S3")
    public AjaxResult uploadFile2S3(@RequestParam String filePath, @RequestParam String s3Key) {
        return AjaxResult.success(s3Service.uploadFileToS3(filePath, s3Key));
    }


    @GetMapping("/upload_directory_2_S3")
    public AjaxResult uploadDirectory2S3(@RequestParam String directoryPath, @RequestParam String s3Key) {
        return AjaxResult.success(s3Service.uploadDirectoryToS3(directoryPath, s3Key));
    }


}