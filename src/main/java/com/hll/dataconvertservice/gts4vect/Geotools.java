package com.hll.dataconvertservice.gts4vect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.hll.dataconvertservice.common.TableNameEnum;
import com.hll.dataconvertservice.entity.DataOutputDesc;
import com.hll.dataconvertservice.gts4vect.osm.DataSet;
import com.hll.dataconvertservice.gts4vect.osm.OsmExporter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.Charsets;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.geotools.data.*;
import org.geotools.data.collection.ListFeatureCollection;
import org.geotools.data.shapefile.ShapefileDataStore;
import org.geotools.data.shapefile.ShapefileDataStoreFactory;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.data.simple.SimpleFeatureStore;
import org.geotools.data.store.ContentFeatureCollection;
import org.geotools.data.store.ContentFeatureSource;
import org.geotools.factory.CommonFactoryFinder;
import org.geotools.feature.FeatureCollection;
import org.geotools.feature.FeatureIterator;
import org.geotools.feature.simple.SimpleFeatureTypeBuilder;
import org.geotools.feature.visitor.CountVisitor;
import org.geotools.geojson.feature.FeatureJSON;
import org.geotools.geojson.geom.GeometryJSON;
import org.geotools.referencing.CRS;
import org.geotools.referencing.crs.DefaultGeographicCRS;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryCollection;
import org.locationtech.jts.io.WKTWriter;
import org.opengis.feature.Feature;
import org.opengis.feature.Property;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;
import org.opengis.feature.type.AttributeDescriptor;
import org.opengis.filter.Filter;
import org.opengis.filter.FilterFactory2;
import org.opengis.filter.expression.PropertyName;
import org.opengis.referencing.crs.CoordinateReferenceSystem;

import org.geotools.data.FeatureSource;
import org.geotools.data.FeatureWriter;
import org.postgresql.jdbc.SslMode;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

import static org.geotools.data.Transaction.AUTO_COMMIT;
import static org.geotools.data.postgis.PostgisNGDataStoreFactory.SSL_MODE;
import static org.geotools.jdbc.JDBCDataStoreFactory.*;

/**
 *
 */
@Slf4j
public class Geotools {

    // private static Logger log = Logger.getLogger(Geotools.class);
    private static DataStore postgisDatasore;

    /**
     * @param postgisDatasore 静态PGDatastore获取默认，实例化PGDatastore指定自定义
     */
    public Geotools(DataStore postgisDatasore) {
        if (postgisDatasore == null) {
            postgisDatasore = PGDatastore.getDefeaultDatastore();
        }
        Geotools.postgisDatasore = postgisDatasore;
    }

    public Geotools() {
        postgisDatasore = PGDatastore.getDefeaultDatastore();
    }

    /**
     * 通用，要素集写入postgis
     *
     * @param featureCollection
     * @param pgtableName       postgis创建的数据表
     * @return
     */
    public static boolean write2pg(FeatureCollection featureCollection, String pgtableName) {
        boolean result = false;
        try {
            if (Utility.isEmpty(featureCollection) || Utility.isEmpty(pgtableName)) {
                log.error("参数无效");
                return result;
            }
            SimpleFeatureType simpleFeatureType = (SimpleFeatureType) featureCollection.getSchema();
            SimpleFeatureTypeBuilder typeBuilder = new SimpleFeatureTypeBuilder();
            typeBuilder.init(simpleFeatureType);

            typeBuilder.setName(pgtableName);

            SimpleFeatureType newtype = typeBuilder.buildFeatureType();
            postgisDatasore.createSchema(newtype);

            FeatureIterator iterator = featureCollection.features();
            FeatureWriter<SimpleFeatureType, SimpleFeature> featureWriter = postgisDatasore.getFeatureWriterAppend(pgtableName, AUTO_COMMIT);

            while (iterator.hasNext()) {
                Feature feature = iterator.next();
                SimpleFeature simpleFeature = featureWriter.next();
                Collection<Property> properties = feature.getProperties();
                Iterator<Property> propertyIterator = properties.iterator();
                while (propertyIterator.hasNext()) {
                    Property property = propertyIterator.next();
                    simpleFeature.setAttribute(property.getName().toString(), property.getValue());
                }
                featureWriter.write();
            }
            iterator.close();
            featureWriter.close();

        } catch (Exception e) {
            log.error("失败", e);
        }
        return false;
    }

    /**
     * featureCollection写入到shp的datastore
     *
     * @param featureCollection
     * @param shpDataStore
     * @param geomFieldName     featureCollectio中的矢量字段，postgis可以修改使用不同的表名，默认为geom
     * @return
     */
    public static boolean write2shp(FeatureCollection featureCollection, ShapefileDataStore shpDataStore, String geomFieldName) {
        boolean result = false;
        if (Utility.isEmpty(geomFieldName)) {
            geomFieldName = featureCollection.getSchema().getGeometryDescriptor().getType().getName().toString();
        }
        try {
            FeatureIterator<SimpleFeature> iterator = featureCollection.features();
            // shp文件存储写入
            FeatureWriter<SimpleFeatureType, SimpleFeature> featureWriter = shpDataStore.getFeatureWriter(shpDataStore.getTypeNames()[0], AUTO_COMMIT);
            while (iterator.hasNext()) {
                Feature feature = iterator.next();
                SimpleFeature simpleFeature = featureWriter.next();
                Collection<Property> properties = feature.getProperties();
                Iterator<Property> propertyIterator = properties.iterator();

                while (propertyIterator.hasNext()) {
                    Property property = propertyIterator.next();
                    if (property.getName().toString().equalsIgnoreCase(geomFieldName)) {
                        simpleFeature.setAttribute("the_geom", property.getValue());
                    } else {
                        if (property.getName().toString().equals("null")) {
                            continue;
                        }
                        if (property.getName().toString().length() > 10) {
                            simpleFeature.setAttribute(property.getName().toString().substring(0, 10), property.getValue());
                        } else {
                            simpleFeature.setAttribute(property.getName().toString(), property.getValue());
                        }
                    }
                }
                featureWriter.write();
            }
            iterator.close();
            featureWriter.close();
            shpDataStore.dispose();
        } catch (Exception e) {
            log.error("失败", e);
        }
        return false;
    }

    /**
     * 方法重载，默认使用UTF-8的Shp文件
     *
     * @param geojsonPath
     * @param shpfilepath
     * @return
     */
    public boolean geojson2shp(String geojsonPath, String shpfilepath) {
        return geojson2shp(geojsonPath, shpfilepath, ShpCharset.UTF_8);
    }

    /**
     * Geojson转成shpfile文件
     *
     * @param geojsonPath
     * @param shpfilepath
     * @return
     */
    public boolean geojson2shp(String geojsonPath, String shpfilepath, Charset shpChart) {
        boolean result = false;
        try {
            Utility.valiFileForRead(geojsonPath);
            FeatureJSON featureJSON = new FeatureJSON();
            featureJSON.setEncodeNullValues(true);
            FeatureCollection featureCollection = featureJSON.readFeatureCollection(new InputStreamReader(new FileInputStream(geojsonPath), "utf-8"));

            File file = new File(shpfilepath);
            Map<String, Serializable> params = new HashMap<String, Serializable>();
            params.put(ShapefileDataStoreFactory.URLP.key, file.toURI().toURL());
            ShapefileDataStore shpDataStore = (ShapefileDataStore) new ShapefileDataStoreFactory().createNewDataStore(params);

            // postgis获取的Featuretype获取坐标系代码
            SimpleFeatureType pgfeaturetype = (SimpleFeatureType) featureCollection.getSchema();

            SimpleFeatureTypeBuilder typeBuilder = new SimpleFeatureTypeBuilder();
            typeBuilder.init(pgfeaturetype);
            typeBuilder.setCRS(DefaultGeographicCRS.WGS84);
            pgfeaturetype = typeBuilder.buildFeatureType();
            // 设置成utf-8编码
            shpDataStore.setCharset(shpChart);
            shpDataStore.createSchema(pgfeaturetype);
            write2shp(featureCollection, shpDataStore, "");
            result = true;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * geojson文件写入到postgis里
     *
     * @param geojsonPath
     * @param pgtableName
     * @return
     */
    public boolean geojson2pgtable(String geojsonPath, String pgtableName) {
        boolean result = false;
        try {
            if (Utility.isEmpty(geojsonPath) || Utility.isEmpty(pgtableName)) {
                return result;
            }
            FeatureJSON featureJSON = new FeatureJSON();
            FeatureCollection featureCollection = featureJSON.readFeatureCollection(new FileInputStream(geojsonPath));
            write2pg(featureCollection, pgtableName);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 重载方法，默认UTF-8编码SHP文件
     *
     * @param shpPath
     * @param geojsonPath
     * @return
     */
    public boolean shp2geojson(String shpPath, String geojsonPath) {
        return shp2geojson(shpPath, geojsonPath, ShpCharset.UTF_8);
    }

    /**
     * shp转成geojson，保留15位小数
     *
     * @param shpPath     shp的路径
     * @param geojsonPath geojson的路径
     * @return
     */
    public boolean shp2geojson(String shpPath, String geojsonPath, Charset shpCharset) {
        boolean result = false;
        try {
            if (!Utility.valiFileForRead(shpPath) || Utility.isEmpty(geojsonPath)) {
                return result;
            }
            ShapefileDataStore shapefileDataStore = new ShapefileDataStore(new File(shpPath).toURI().toURL());
            shapefileDataStore.setCharset(shpCharset);
            ContentFeatureSource featureSource = shapefileDataStore.getFeatureSource();
            ContentFeatureCollection contentFeatureCollection = featureSource.getFeatures();
            FeatureJSON featureJSON = new FeatureJSON(new GeometryJSON(15));
            Utility.valiFileForWrite(geojsonPath);
            featureJSON.writeFeatureCollection(contentFeatureCollection, new File(geojsonPath));
            shapefileDataStore.dispose();
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    public boolean shp2pgtable(String shpPath, String pgtableName) {
        return shp2pgtable(shpPath, pgtableName, ShpCharset.UTF_8);
    }

    /**
     * shpfile文件导入到postgis中
     *
     * @param shpPath
     * @param pgtableName
     * @return
     */
    public boolean shp2pgtable(String shpPath, String pgtableName, Charset shpCharset) {
        boolean result = false;
        try {
            ShapefileDataStore shapefileDataStore = new ShapefileDataStore(new File(shpPath).toURI().toURL());
            shapefileDataStore.setCharset(shpCharset);
            FeatureCollection featureCollection = shapefileDataStore.getFeatureSource().getFeatures();
            write2pg(featureCollection, pgtableName);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * postgis数据表导出到成shpfile
     *
     * @param pgtableName
     * @param shpPath
     * @param geomField   postgis里的字段
     * @return
     */
    public boolean pgtable2shp(String pgtableName, String shpPath, String geomField) {
        boolean result = false;
        try {

            FeatureSource featureSource = postgisDatasore.getFeatureSource(pgtableName);

            // 初始化 ShapefileDataStore
            File file = new File(shpPath);
            Map<String, Serializable> params = new HashMap<String, Serializable>();
            params.put(ShapefileDataStoreFactory.URLP.key, file.toURI().toURL());
            ShapefileDataStore shpDataStore = (ShapefileDataStore) new ShapefileDataStoreFactory().createNewDataStore(params);

            // postgis获取的Featuretype获取坐标系代码
            SimpleFeatureType pgfeaturetype = ((SimpleFeatureSource) featureSource).getSchema();
            String srid = pgfeaturetype.getGeometryDescriptor().getUserData().get("nativeSRID").toString();
            SimpleFeatureTypeBuilder typeBuilder = new SimpleFeatureTypeBuilder();
            typeBuilder.init(pgfeaturetype);
            if (!"0".equals(srid)) {
                CoordinateReferenceSystem crs = CRS.decode("EPSG:" + srid, true);
                typeBuilder.setCRS(crs);
            }
            pgfeaturetype = typeBuilder.buildFeatureType();
            // 设置成utf-8编码
            shpDataStore.setCharset(Charset.forName("utf-8"));
            shpDataStore.createSchema(pgfeaturetype);
            write2shp(featureSource.getFeatures(), shpDataStore, geomField);
            result = true;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * postgis指定的数据表转成geojson文件保留15位小数
     *
     * @param pgtableName 表名
     * @param geojsonpath geojson存放位置
     * @return
     */
    public boolean pgtable2geojson(String pgtableName, String geojsonpath) {
        boolean result = false;
        try {
            SimpleFeatureSource featureSource = postgisDatasore.getFeatureSource(pgtableName);
            SimpleFeatureCollection featureCollection = featureSource.getFeatures();
            FeatureJSON featureJSON = new FeatureJSON(new GeometryJSON(15));
            int size = featureCollection.size();
            System.out.println("process start. " + size + " records to process");
            File outputJsonFile = new File(geojsonpath);
            List<SimpleFeature> featureList = new ArrayList<>();
            // 拿到迭代器
            SimpleFeatureIterator featureIterator = featureCollection.features();
            // 遍历每一个要素
            int i = 0;
            int j = 0;
            SimpleFeatureType simpleFeatureType = null;
            while (featureIterator.hasNext()) {
                i++;
                SimpleFeature feature = featureIterator.next();
                if (i == 1) {
                    simpleFeatureType = feature.getFeatureType();
                    System.out.println("feature type is: " + simpleFeatureType);
                }
//                if(i > 2400000){
                featureList.add(feature);
//                }
                // 每100000条保存一次
                if (i % 200000 == 0) {
//                    j++;
//                    if(j == 3){
                    SimpleFeatureCollection collection = new ListFeatureCollection(simpleFeatureType, featureList);
                    String s = featureJSON.toString(collection);
                    FileUtils.writeStringToFile(outputJsonFile, s, Charsets.toCharset("utf-8"), true);
//                    }
                    featureList.clear();
                    System.out.println("processed " + i + " of " + size);
                }
            }
            System.out.println("process the last  " + featureList.size() + " records");
            System.out.println("feature type is: " + simpleFeatureType);
            SimpleFeatureCollection collection = new ListFeatureCollection(simpleFeatureType, featureList);
            System.out.println("last collection size is: " + collection.size());
            String s = featureJSON.toString(collection);
            FileUtils.writeStringToFile(outputJsonFile, s, Charsets.toCharset("utf-8"), true);
            System.out.println("process finished");
            result = true;

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }


    /**
     * 构建组合过滤器示例，可灵活添加更多条件
     */
    public static Filter buildCombinedFilter() {
        FilterFactory2 ff = CommonFactoryFinder.getFilterFactory2(null);

        // 存放单个条件的列表
        List<Filter> andFilters = new ArrayList<>();

        // condition1: status != 1
        PropertyName statusField = ff.property("status");
        Filter statusNotEquals1 = ff.not(ff.equals(statusField, ff.literal(1)));
        andFilters.add(statusNotEquals1);

        // condition2: height >= 100
        // PropertyName heightField = ff.property("height");
        // Filter heightGreaterOrEquals100 = ff.greaterOrEqual(heightField, ff.literal(100));
        // andFilters.add(heightGreaterOrEquals100);

        // 你可以继续往 andFilters 中添加更多的 Filter

        // 最后将所有条件用 AND 组合
        // 如果想用 OR，可以改为 ff.or(andFilters)
        return ff.and(andFilters);
    }

    private String formatGeoJSON(String geojsonStr) {
        // ① 判空
        if (geojsonStr == null) {
            return null;
        }

        // ② 去掉首尾空白（包括 \r \n \t）
        geojsonStr = geojsonStr.trim();

        // ③ 兼容意外的前导/尾随逗号
        if (geojsonStr.startsWith(",")) {
            geojsonStr = geojsonStr.substring(1).trim();
        }
        if (geojsonStr.endsWith(",")) {
            geojsonStr = geojsonStr.substring(0, geojsonStr.length() - 1).trim();
        }

        try {
            ObjectMapper mapper = new ObjectMapper()
                    .enable(JsonParser.Feature.ALLOW_TRAILING_COMMA) // 允许数组/对象末尾逗号
                    .enable(JsonParser.Feature.ALLOW_COMMENTS);      // 允许 // 或 /* */ 注释

            JsonNode root = mapper.readTree(geojsonStr);

            // 只处理 FeatureCollection，其他类型直接返回
            if (!"FeatureCollection".equals(root.path("type").asText())) {
                return geojsonStr;
            }

            ArrayNode features = (ArrayNode) root.path("features");
            StringBuilder sb = new StringBuilder();
            sb.append("{\"type\":\"FeatureCollection\",\"features\":[\n");

            for (int i = 0; i < features.size(); i++) {
                // 单行输出每个 Feature
                sb.append(mapper.writeValueAsString(features.get(i)));
                if (i < features.size() - 1) {
                    sb.append(",\n");   // 只有不是最后一个元素时才加逗号
                }
            }
            sb.append("\n]}");
            return sb.toString();
        } catch (Exception e) {
            log.error("GeoJSON 格式化失败: " + e.getMessage(), e);
            return geojsonStr;   // 出错时保持原字符串，避免数据丢失
        }
    }

    // /**
    //  * 将GeoJSON字符串格式化，使每个feature在新行显示
    //  * @param geojsonStr 原始GeoJSON字符串
    //  * @return 格式化后的GeoJSON字符串
    //  */
    // private String formatGeoJSON(String geojsonStr) {
    //     // 使用Fastjson解析和格式化
    //     JSONObject jsonObject = JSONObject.parseObject(geojsonStr);
    //     JSONArray features = jsonObject.getJSONArray("features");
    //
    //     StringBuilder sb = new StringBuilder();
    //     sb.append("{\"type\":\"").append(jsonObject.getString("type")).append("\",\"features\":[\n");
    //
    //     for (int i = 0; i < features.size(); i++) {
    //         JSONObject feature = features.getJSONObject(i);
    //         String featureStr = JSONObject.toJSONString(feature, SerializerFeature.WriteMapNullValue);
    //         sb.append(featureStr);
    //
    //         // 如果不是最后一个feature，添加逗号和换行符
    //         if (i < features.size() - 1) {
    //             sb.append(",\n");
    //         }
    //     }
    //
    //     sb.append("\n]}");
    //     return sb.toString();
    // }

//     public boolean pgtable2geojson(String pgtableName, String geojsonpath, String pidName, String[] excludeFields) {
//         boolean result = false;
//
//         try {
//             // 1.获取所有字段
//             SimpleFeatureType schema = postgisDatasore.getSchema(pgtableName);
//             List<String> allFields = new ArrayList<>();
//
//             // 2.创建排除字段的列表
//             List<String> excludeList = excludeFields != null ? Arrays.asList(excludeFields) : Collections.emptyList();
//
//             // 3.获取所有属性名称
//             for (AttributeDescriptor descriptor : schema.getAttributeDescriptors()) {
//                 // System.out.println(descriptor.getLocalName() + ": " + descriptor.getType().getBinding().getSimpleName());
//                 String fieldName = descriptor.getLocalName();
//                 if (excludeList.isEmpty() || !excludeList.contains(fieldName)) {
//                     allFields.add(fieldName);
//                 }
//             }
//             // 4.创建query对象，设置需要的字段
//             Query query = new Query(pgtableName, buildCombinedFilter());
//             query.setPropertyNames(allFields);
//             // query.setMaxFeatures(20);
//
//             // Filter filter = CQL.toFilter("status != 1");
//             // query.setFilter(filter);
//             // 设置最大返回记录数，相当于 LIMIT 20
//             // query.setMaxFeatures(20);
//             // 设置起始位置，相当于 OFFSET 0
//             // query.setStartIndex(0);
//
//
//             SimpleFeatureSource featureSource = postgisDatasore.getFeatureSource(pgtableName);
//             // SimpleFeatureCollection featureCollection = featureSource.getFeatures();
//             SimpleFeatureCollection featureCollection = featureSource.getFeatures(query);
//             FeatureJSON featureJSON = new FeatureJSON(new GeometryJSON(15));
//             int geojsonNum = 0;
//             // 允许空值字段带出
//             featureJSON.setEncodeNullValues(true);
//             int size = featureCollection.size();
//             System.out.println("process start. " + size + " records to process");
//             File outputJsonFile = new File(geojsonpath);
//             List<SimpleFeature> featureList = new ArrayList<>();
//             // 拿到迭代器
//             SimpleFeatureIterator featureIterator = featureCollection.features();
//             // 遍历每一个要素
//             int i = 0;
//             int j = 0;
//             int count = 200000;
//             SimpleFeatureType simpleFeatureType = null;
//             while (featureIterator.hasNext()) {
//                 i++;
//                 SimpleFeature feature = featureIterator.next();
//                 if (i == 1) {
//                     simpleFeatureType = feature.getFeatureType();
//                     System.out.println("feature type is: " + simpleFeatureType);
//                 }
// //                if(i > 2400000){
//                 featureList.add(feature);
// //                }
// //                 if(i % count == 0){
//                 if (i % count == 0) {
//                     j++;
//                     // if(j == 3){
//                     SimpleFeatureCollection collection = new ListFeatureCollection(simpleFeatureType, featureList);
//                     String s = featureJSON.toString(collection);
//                     if (size % count == 0) {
//                         if (size == count && j == 1) {
//                             String results = processPidAndNum(s, pidName);
//                             results = formatGeoJSON(results);
//                             FileUtils.writeStringToFile(outputJsonFile, results, Charsets.toCharset("utf-8"), true);
//                         }
//                         if (size > count && j == 1) {
//                             String results = processPidAndNum(s, pidName);
//                             results = processTail(results, true, j);
//                             results = formatGeoJSON(results);
//                             FileUtils.writeStringToFile(outputJsonFile, results, Charsets.toCharset("utf-8"), true);
//                         }
//                         if (j > 1 && j < size / count) {
//                             String results = processPidAndNum(s, pidName);
//                             results = processTail(results, true, j);
//                             results = formatGeoJSON(results);
//                             FileUtils.writeStringToFile(outputJsonFile, results, Charsets.toCharset("utf-8"), true);
//                         }
//                         if (j == size / count && j != 1) {
//                             String results = processPidAndNum(s, pidName);
//                             results = processTail(results, false, j);
//                             results = formatGeoJSON(results);
//                             FileUtils.writeStringToFile(outputJsonFile, results, Charsets.toCharset("utf-8"), true);
//                         }
//                     } else {
//                         String results = processPidAndNum(s, pidName);
//                         if (size > count) {
//                             results = processTail(results, true, j);
//                         }
//                         results = formatGeoJSON(results);
//                         FileUtils.writeStringToFile(outputJsonFile, results, Charsets.toCharset("utf-8"), true);
//                     }
// //                    }
//                     geojsonNum += featureList.size();
//                     featureList.clear();
//                     // featureList = new ArrayList<>();
//                     System.out.println("processed " + i + " of " + size);
//                 }
//             }
//             if (featureList.size() > 0) {
//                 System.out.println("process the " + featureList.size() + " records");
//                 System.out.println("feature type is: " + simpleFeatureType);
//                 SimpleFeatureCollection collection = new ListFeatureCollection(simpleFeatureType, featureList);
//                 System.out.println(" collection size is: " + collection.size());
//                 String s = featureJSON.toString(collection);
//                 // s = processNum(s);
//                 // s = s.replace("geometry",System.lineSeparator()+"geometry");
//                 String results = processPidAndNum(s, pidName);
//                 if (featureList.size() < count && j > 0) {
//                     results = processTail(results, false, null);
//                 }
//                 // geometry字段换行，后期有需要放开，需在每个写文件方法前加
//                 // results = results.replace("{\"geometry",System.lineSeparator()+"{\"geometry");
//                 results = formatGeoJSON(results);
//                 FileUtils.writeStringToFile(outputJsonFile, results, Charsets.toCharset("utf-8"), true);
//                 geojsonNum += featureList.size();
//                 System.out.println("process finished");
//             }
//             logger.info("process finished,file is [" + geojsonpath + "], total records: [" + geojsonNum + "]");
//             System.out.println("-----process finished-----,file is [" + geojsonpath + "], total records: [" + geojsonNum + "]");
//             result = true;
//         } catch (Exception e) {
//             e.printStackTrace();
//             logger.error(e.getMessage(), e);
//             return false;
//         }
//         return result;
//     }


    /**
     * 将 PostGIS 表导出为 GeoJSON（15 位小数），并可
     * ① 过滤掉指定字段；② 把 id 拆到 properties(pidName) 中；
     * ③ 批量写文件，避免一次性序列化过大。
     */
    public boolean pgtable2geojson(String pgtableName,
                                   String geojsonpath,
                                   String pidName,
                                   String[] excludeFields) {

        boolean result = false;

        try {
            /* ---------- 1. 获取字段，并处理排除列表 ---------- */
            SimpleFeatureType schema = postgisDatasore.getSchema(pgtableName);
            List<String> allFields = new ArrayList<>();

            // 2. 创建排除字段列表
            List<String> excludeList =
                    excludeFields != null ? Arrays.asList(excludeFields) : Collections.emptyList();

            // 3. 仅保留未被排除的字段
            for (AttributeDescriptor descriptor : schema.getAttributeDescriptors()) {
                String fieldName = descriptor.getLocalName();
                if (excludeList.isEmpty() || !excludeList.contains(fieldName)) {
                    allFields.add(fieldName);
                }
            }

            /* ---------- 4. 构造查询 ---------- */
            Query query = new Query(pgtableName, buildCombinedFilter());
            query.setPropertyNames(allFields);

            SimpleFeatureSource featureSource = postgisDatasore.getFeatureSource(pgtableName);
            SimpleFeatureCollection featureCollection = featureSource.getFeatures(query);

            FeatureJSON featureJSON = new FeatureJSON(new GeometryJSON(15));
            featureJSON.setEncodeNullValues(true);               // 允许空值带出

            int size = featureCollection.size();
            System.out.println("process start. " + size + " records to process");

            File outputJsonFile = new File(geojsonpath);
            List<SimpleFeature> featureList = new ArrayList<>();

            /* ---------- 5. 迭代写出，分批 200 000 ---------- */
            SimpleFeatureIterator featureIterator = featureCollection.features();
            int i = 0, j = 0, count = 200_000, geojsonNum = 0;
            SimpleFeatureType simpleFeatureType = null;

            while (featureIterator.hasNext()) {
                i++;
                SimpleFeature feature = featureIterator.next();
                if (i == 1) {
                    simpleFeatureType = feature.getFeatureType();
                    System.out.println("feature type is: " + simpleFeatureType);
                }
                featureList.add(feature);

                if (i % count == 0) {
                    j++;
                    SimpleFeatureCollection collection =
                            new ListFeatureCollection(simpleFeatureType, featureList);
                    String s = featureJSON.toString(collection);

                    // ---------- 6. 首、中、尾段裁剪拼接 ----------
                    if (size % count == 0) {
                        if (size == count && j == 1) {                                   // 仅一段
                            FileUtils.writeStringToFile(
                                    outputJsonFile,
                                    formatGeoJSON(processPidAndNum(s, pidName)),
                                    Charsets.toCharset("utf-8"), true);
                        } else {                                                         // 多段
                            String res = formatGeoJSON(processPidAndNum(s, pidName));
                            boolean first = (j == 1);
                            boolean last = (j == size / count);
                            res = processTail(res, !last, j);        // true→去掉尾括号，保留逗号
                            FileUtils.writeStringToFile(
                                    outputJsonFile, res, Charsets.toCharset("utf-8"), true);
                        }
                    } else {                                     // size 不能整除 count
                        String res = formatGeoJSON(processPidAndNum(s, pidName));
                        if (size > count) res = processTail(res, true, j);
                        FileUtils.writeStringToFile(
                                outputJsonFile, res, Charsets.toCharset("utf-8"), true);
                    }

                    geojsonNum += featureList.size();
                    featureList.clear();
                    System.out.println("processed " + i + " of " + size);
                }
            }

            /* ---------- 7. 处理尾批 ---------- */
            if (!featureList.isEmpty()) {
                SimpleFeatureCollection collection =
                        new ListFeatureCollection(simpleFeatureType, featureList);
                String res = formatGeoJSON(processPidAndNum(featureJSON.toString(collection), pidName));
                if (featureList.size() < count && j > 0) {
                    res = processTail(res, false, null);          // 最后一批：补齐 ] }
                }
                FileUtils.writeStringToFile(outputJsonFile, res, Charsets.toCharset("utf-8"), true);
                geojsonNum += featureList.size();
            }

            log.info("process finished, file [" + geojsonpath + "], total " + geojsonNum);
            result = true;

        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
            return false;
        }
        return result;
    }

    private String processTail(String results, boolean b, Integer num) {
        if (b) {
            if (num == 1) {
                // 第一次处理
                return results.substring(0, results.lastIndexOf("]"));
            } else {
                // 中间处理
                return "," + results.substring(results.indexOf("[") + 1, results.lastIndexOf("]"));
            }
        } else {
            // 最后一次处理
            return "," + results.substring(results.indexOf("[") + 1);
        }
    }

    // ValueFilter filter = new ValueFilter() {
    //     @Override
    //     public Object process(Object object, String name, Object value) {
    //         if (value instanceof BigDecimal || value instanceof Double || value instanceof Float) {
    //             return new BigDecimal(value.toString());
    //         }
    //         if (name.equals("coordinates")){
    //             JSONArray coordinates = (JSONArray)value;
    //             JSONArray jsonArray = (JSONArray) coordinates.get(0);
    //             for (int j = 0; j < jsonArray.size(); j++) {
    //                 JSONArray jsonArray1 = jsonArray.getJSONArray(j);
    //                 for (int m = 0; m < jsonArray1.size(); m++) {
    //                     jsonArray1.set(m, jsonArray1.getDouble(m));
    //                 }
    //             }
    //         }
    //         return value;
    //     }
    // };
    private String processNum(String s) {
        JSONObject sJson = JSONObject.parseObject(s);
        JSONArray features = sJson.getJSONArray("features");
        for (int i = 0; i < features.size(); i++) {
            JSONObject featureOne = features.getJSONObject(i);
            JSONObject properties = featureOne.getJSONObject("geometry");
            if (properties == null) {
                continue;
            }
            JSONArray coordinates = properties.getJSONArray("coordinates");
            if (coordinates.get(0) instanceof BigDecimal) {
                for (int j = 0; j < coordinates.size(); j++) {
                    BigDecimal bigDecimal = coordinates.getBigDecimal(j);
                    coordinates.set(j, bigDecimal.doubleValue());
                }
            } else if (coordinates.get(0) instanceof Integer) {
                for (int j = 0; j < coordinates.size(); j++) {
                    Integer integer = coordinates.getInteger(j);
                    coordinates.set(j, integer.doubleValue());
                }
            } else {
                JSONArray jsonArray = (JSONArray) coordinates.get(0);
                for (int j = 0; j < jsonArray.size(); j++) {
                    JSONArray jsonArray1 = jsonArray.getJSONArray(j);
                    for (int k = 0; k < jsonArray1.size(); k++) {
                        jsonArray1.set(k, jsonArray1.getDouble(k));
                    }
                }
            }
        }
        return JSONObject.toJSONString(sJson, SerializerFeature.WriteMapNullValue);
    }


    public String processPidAndNum(String s, String pidName) {
        JSONObject root = JSONObject.parseObject(s);
        JSONArray features = root.getJSONArray("features");

        for (int k = 0; k < features.size(); k++) {
            JSONObject feature = features.getJSONObject(k);
            JSONObject props = feature.getJSONObject("properties");
            String pid = feature.getString("id").split("\\.")[1];

            feature.remove("id");
            props.put(pidName, pid);

            JSONObject geometry = feature.getJSONObject("geometry");
            if (geometry == null) continue;

            normalizeArray(geometry.getJSONArray("coordinates"));
        }
        return JSONObject.toJSONString(root, SerializerFeature.WriteMapNullValue);
    }

    private void normalizeArray(JSONArray arr) {
        for (int i = 0; i < arr.size(); i++) {
            Object v = arr.get(i);
            if (v instanceof JSONArray) {
                normalizeArray((JSONArray) v);
            } else if (v instanceof Number) {
                arr.set(i, ((Number) v).doubleValue());   // 或改成保留 BigDecimal
            }
        }
    }

    // public String processPidAndNum(String s, String pidName) {
    //     JSONObject sJson = JSONObject.parseObject(s);
    //     JSONArray features = sJson.getJSONArray("features");
    //     for (int k = 0; k < features.size(); k++) {
    //         JSONObject featureOne = features.getJSONObject(k);
    //         JSONObject properties = featureOne.getJSONObject("properties");
    //         String futureId = featureOne.get("id").toString();
    //         String pid = futureId.split("\\.")[1];
    //         // 移除id，propteries增加主键id
    //         featureOne.remove("id");
    //         properties.put(pidName, pid);
    //         JSONObject properties1 = featureOne.getJSONObject("geometry");
    //         if (properties1 == null) {
    //             continue;
    //         }
    //         JSONArray coordinates = properties1.getJSONArray("coordinates");
    //         if (coordinates.get(0) instanceof BigDecimal) {
    //             for (int j = 0; j < coordinates.size(); j++) {
    //                 BigDecimal bigDecimal = coordinates.getBigDecimal(j);
    //                 coordinates.set(j, bigDecimal.doubleValue());
    //             }
    //         } else if (coordinates.get(0) instanceof Integer) {
    //             for (int j = 0; j < coordinates.size(); j++) {
    //                 Integer integer = coordinates.getInteger(j);
    //                 coordinates.set(j, integer.doubleValue());
    //             }
    //         } else {
    //             for (int i = 0; i < coordinates.size(); i++) {
    //                 JSONArray jsonArray = (JSONArray) coordinates.get(i);
    //                 for (int j = 0; j < jsonArray.size(); j++) {
    //                     // JSONArray jsonArray1 = jsonArray.getJSONArray(j);
    //                     // for (int m = 0; m < jsonArray1.size(); m++) {
    //                     //     jsonArray1.set(m, jsonArray1.getDouble(m));
    //                     // }
    //                     // jsonArray.set(j, jsonArray.getDoubleValue(j));
    //                     if (jsonArray.get(j) instanceof JSONArray) {
    //                         JSONArray jsonArray1 = jsonArray.getJSONArray(j);
    //                         for (int m = 0; m < jsonArray1.size(); m++) {
    //                             if (jsonArray1.get(m) instanceof Double)
    //                                 jsonArray1.set(m, jsonArray1.getDouble(m));
    //                             if (jsonArray1.get(m) instanceof BigDecimal)
    //                                 jsonArray1.set(m, jsonArray1.getDouble(m));
    //                         }
    //                     } else {
    //                         jsonArray.set(j, jsonArray.getDoubleValue(j));
    //                     }
    //                 }
    //             }
    //             // return JSONObject.toJSONString(sJson,filter,SerializerFeature.WriteMapNullValue);
    //         }
    //     }
    //     return JSONObject.toJSONString(sJson, SerializerFeature.WriteMapNullValue);
    // }

    public boolean deletePgtable(String pgtableName) {
        boolean result = false;
        try {
            postgisDatasore.removeSchema(pgtableName);
            result = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /*
    //    测试调试专用，成功清除所有的sw开头的表（用来存储矢量数据的表）
        public boolean clearSWTable() throws Exception {
            postgisDatasore.removeSchema();
            //relkind char r = 普通表，i = 索 引， S = 序列，v = 视 图， m = 物化视图， c = 组合类型，t = TOAST表， f = 外部 表
            String strtables = " select string_agg(relname ,\',\') from pg_class where relname like \'%sw_%\'  and relkind=\'r\' ";
            List list =  postgisDatasore.getSessionFactory().getCurrentSession().createQuery(strtables).list();
            list.get(0).toString();
            Integer integer = 0;
            if (list.size() > 0) {
                integer = temp.getSessionFactory().getCurrentSession().createQuery("drop table " + strtables).executeUpdate();
            }
    //        与表有关联的其他序列自动删除
            String sequence = " select string_agg(relname ,\',\') from pg_class where relname like \'%sw_%\' and relkind=\'S\' and relname!=\'txsw_seq\'";
            resultSet = st.executeQuery(sequence);
            while (resultSet.next()) {
                sequence = resultSet.getString(1);
            }
            System.out.println("所有非txsw_seq的序列：" + sequence);
            i = st.executeUpdate("drop SEQUENCE " + strtables);
            return integer == 0 ? true : false;
        }
    */
    public static boolean testCreatFeature(String featurePath) {
        boolean result = false;
        try {


            result = true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

    /* 通过sql流入方式导入pg 可通过下面方式调用
    *   PGDatastore pgDatastore = new PGDatastore();
        Map<String, Object> params = new HashMap<String, Object>();
        params.put(PostgisNGDataStoreFactory.DBTYPE.key, "postgis");
        params.put(PostgisNGDataStoreFactory.HOST.key, "***************");
        params.put(PostgisNGDataStoreFactory.PORT.key, new Integer(5432));
        params.put(PostgisNGDataStoreFactory.DATABASE.key , "here_tha");
        params.put(PostgisNGDataStoreFactory.SCHEMA.key, "public");
        params.put(PostgisNGDataStoreFactory.USER.key, "postgres");
        params.put(PostgisNGDataStoreFactory.PASSWD.key, "1q2w3e");

        DataStore dataStore = DataStoreFinder.getDataStore(params);

        String shpfilepath = "/Users/<USER>/Downloads/JNCN211F0WJN000AACVH/J2AM211F0WJ2000AACVH/J2AM211F0WJ2000AACVH/Streets.shp";
        String pgtableName = "MuchBigPolygon_1";
        Charset shpCharset = Charset.forName("utf-8");

        Geotools geotools = new Geotools(dataStore);
        geotools.shp2pgInjector(params,shpfilepath,pgtableName,shpCharset);
    */
    public boolean shp2pgInjector(Map<String, Object> params, String shpPath, String pgtableName, Charset shpCharset) {
        try {
            ShapefileDataStore shapefileDataStore = new ShapefileDataStore(new File(shpPath).toURI().toURL());
            shapefileDataStore.setCharset(shpCharset);

            Connection connection = null;
            PreparedStatement preparedStatement = null;

            if (postgisDatasore != null) {
                List<AttributeDescriptor> attrList = shapefileDataStore.getSchema().getAttributeDescriptors();
                FeatureWriter<SimpleFeatureType, SimpleFeature> writer = createFeaClass2(shapefileDataStore, postgisDatasore, attrList, pgtableName.toLowerCase());
                SimpleFeatureStore pgFeatureDatastore = (SimpleFeatureStore) postgisDatasore.getFeatureSource(pgtableName.toLowerCase());
                if (writer != null) {
                    connection = DriverManager.getConnection(this.getJDBCUrl(params), params.get(USER.key).toString(), params.get(PASSWD.key).toString());
                    FeatureSource<SimpleFeatureType, SimpleFeature> featureSource = null;
                    // getTypeNames:获取所有地理图层
                    String typeName = shapefileDataStore.getTypeNames()[0];
                    featureSource = shapefileDataStore.getFeatureSource(typeName);
                    // 一个用于处理FeatureCollection的实用工具类。提供一个获取FeatureCollection实例的机制
                    FeatureCollection<SimpleFeatureType, SimpleFeature> result = featureSource.getFeatures();
                    FeatureIterator<SimpleFeature> itertor = result.features();

                    Map<String, Object> mapValues = new LinkedHashMap<>();
                    List<AttributeDescriptor> pgAttrList = pgFeatureDatastore.getSchema().getAttributeDescriptors();
                    String strInsertSql = getPgClassInsertSql(pgtableName.toLowerCase(), pgAttrList, mapValues);
                    preparedStatement = connection.prepareStatement(strInsertSql);
                    Integer index = 1;
                    while (itertor.hasNext()) {
                        SimpleFeature feature = itertor.next();
                        Collection<Property> p = feature.getProperties();
                        Iterator<Property> it = p.iterator();
                        while (it.hasNext()) {
                            Property pro = it.next();
                            String strFieldName = pro.getName().toString().toLowerCase();
                            if ("the_geom".equals(strFieldName)) {
                                strFieldName = "geom";
                            }
                            if (mapValues.containsKey(strFieldName)) {
                                mapValues.put(strFieldName, pro.getValue());
                            }
                        }

                        mapValues.put("fid", index);

                        Integer MapIndex = 1;
                        for (String key : mapValues.keySet()) {
                            if (key.equals("geom")) {
                                preparedStatement.setString(MapIndex++, mapValues.get(key).toString());
                            } else {
                                preparedStatement.setObject(MapIndex++, mapValues.get(key));
                            }
                        }
                        preparedStatement.addBatch();
                        index++;
                        if (index % 10000 == 0) {
                            log.info(index.toString());
                            preparedStatement.executeBatch();
                            preparedStatement.clearBatch();
                        }
                    }
                    itertor.close();
                    preparedStatement.executeBatch();

                    postgisDatasore.dispose();// 使用之后必须关掉
                    shapefileDataStore.dispose();// 使用之后必须关掉
                    return true;
                }
            }
            return false;
        } catch (IOException | SQLException e) {
            e.printStackTrace();
        }
        return false;
    }

    private String getPgClassInsertSql(String strFeaClassName, List<AttributeDescriptor> pgAttrList, Map<String, Object> mapValues) {
        List<String> listValues = new ArrayList<>();
        // FID字段
        listValues.add("?");
        mapValues.put("fid", null);

        for (int i = 0; i < pgAttrList.size(); i++) {
            AttributeDescriptor attributeDescriptor = pgAttrList.get(i);
            if (attributeDescriptor.getName().toString().equals("geom")) {
                listValues.add("st_geomfromText(?,4326)");
            } else {
                listValues.add("?");
            }
            mapValues.put(attributeDescriptor.getName().toString(), null);
        }
        String strInsertSql = String.format("insert into %s values(%s)", strFeaClassName, String.join(",", listValues));
        return strInsertSql;
    }

    /**
     * 通过Shapefile文件创建要素类
     *
     * @param shapefileDataStore
     * @param dataStore
     * @param attrList
     * @param strFeaClassName
     * @return
     */
    public FeatureWriter<SimpleFeatureType, SimpleFeature> createFeaClass(ShapefileDataStore shapefileDataStore, DataStore dataStore, List<AttributeDescriptor> attrList, String strFeaClassName) {

        try {
            // SimpleFeatureTypeBuilder 构造简单特性类型的构造器
            SimpleFeatureTypeBuilder tBuilder = new SimpleFeatureTypeBuilder();
            tBuilder.setName(strFeaClassName);

            SimpleFeatureType schema = shapefileDataStore.getSchema();
            CoordinateReferenceSystem dataCrs = schema.getCoordinateReferenceSystem();
            tBuilder.setCRS(dataCrs);

            for (int i = 0; i < attrList.size(); i++) {
                AttributeDescriptor attributeDescriptor = attrList.get(i);
                Class<?> classType = (Class.forName(attributeDescriptor.getType().getBinding().getName()));
                if (!Double.class.isAssignableFrom(classType) && !Float.class.isAssignableFrom(classType)) {
                    if (GeometryCollection.class.isAssignableFrom(classType)) {
                        tBuilder.add("geom", classType);
                    } else {
                        tBuilder.add(attributeDescriptor.getName().toString().toLowerCase(), classType);
                    }
                } else {
                    tBuilder.add(attributeDescriptor.getName().toString().toLowerCase(), BigDecimal.class);
                }
            }

            // tBuilder.add("CHECKUNIQUEID",Integer.class);

            // 设置此数据存储的特征类型
            dataStore.createSchema(tBuilder.buildFeatureType());
            FeatureWriter<SimpleFeatureType, SimpleFeature> writer = dataStore.getFeatureWriterAppend(strFeaClassName, Transaction.AUTO_COMMIT);
            return writer;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public String getJDBCUrl(Map params) throws IOException {
        String host = (String) HOST.lookUp(params);
        String db = (String) DATABASE.lookUp(params);
        int port = (Integer) PORT.lookUp(params);
        String url = "jdbc:postgresql" + "://" + host + ":" + port + "/" + db;
        SslMode mode = (SslMode) SSL_MODE.lookUp(params);
        if (mode != null) {
            url = url + "?sslmode=" + mode + "&binaryTransferEnable=bytea";
        }

        return url;
    }

    /**
     * 读
     **/
    public static void preprocessGeojsonFile(String filePath) throws IOException {

        JSONObject jsonObject = JSON.parseObject(IOUtils.toString(new FileInputStream(filePath), "utf-8"));

        JSONArray jsonArray = (JSONArray) jsonObject.get("features");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObjecti = (JSONObject) jsonArray.get(i);
            JSONObject jsonGeo = (JSONObject) jsonObjecti.get("geometry");
            if ("Point".equals(jsonGeo.get("type").toString())) {
                JSONArray pointGeo = (JSONArray) jsonGeo.get("coordinates");
                double lat = Double.parseDouble(String.format("%.4f", Double.parseDouble(pointGeo.get(0).toString()) / 100.0));
                double lon = Double.parseDouble(String.format("%.4f", Double.parseDouble(pointGeo.get(1).toString()) / 100.0));

                jsonGeo.put("coordinates", new Double[]{lat, lon});
                System.out.println("coordinates is:" + "[" + lat + "," + lon + "]");
            } else if ("LineString".equals(jsonGeo.get("type").toString())) {
                JSONArray lineGeoList = (JSONArray) jsonGeo.get("coordinates");
                List<List<Double>> coordinatesList = new ArrayList<>();
                for (int j = 0; j < lineGeoList.size(); j++) {
                    List<Double> coordinatesL = new ArrayList<>();
                    JSONArray lineGeo = (JSONArray) lineGeoList.get(j);
                    double lon = Double.parseDouble(String.format("%.4f", Double.parseDouble(lineGeo.get(0).toString()) / 100.0));
                    double lat = Double.parseDouble(String.format("%.4f", Double.parseDouble(lineGeo.get(1).toString()) / 100.0));
                    coordinatesL.add(lon);
                    coordinatesL.add(lat);
                    coordinatesList.add(coordinatesL);
                }
                jsonGeo.put("coordinates", coordinatesList);
            }
        }

        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath.substring(0, filePath.lastIndexOf(".")) + "_outfile.geojson", false), "UTF-8"));
        writer.write("");
        writer.write(jsonObject.toString());
        writer.close();
    }


    /**
     * 写
     **/
    public void jsonOutPut(Map map) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            mapper.writeValue(new File("D:/river-site.json"), map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws IOException {
        // String filePath ="/Users/<USER>/Documents/personal/project/routing/sourceInfo/deck10.geojson";
        String filePath = "/Users/<USER>/Downloads/deck09.geojson";
        String outPath = filePath.substring(0, filePath.lastIndexOf(".")) + "_outosm.osm";
        // preprocessGeojsonFile(filePath);

        DataSet jsonData = GeoJSONReader.parseDataSet(new FileInputStream(filePath));

        System.out.println("finined readJson");
        OsmExporter.doSave(new File(outPath), jsonData);
        System.out.println("finined writeOsm");
        // new GeoJSONReader().doParseDataSet(new FileInputStream(filePath));
    }

    public boolean shp2pgInjector2(Map<String, Object> params, String shpPath, String pgtableName, Charset shpCharset) {
        // 使用 try-with-resources 确保 ShapefileDataStore 被关闭
        ShapefileDataStore shapefileDataStore = null;
        try {
            shapefileDataStore = new ShapefileDataStore(new File(shpPath).toURI().toURL());
            shapefileDataStore.setCharset(shpCharset);

            if (postgisDatasore != null) {
                List<AttributeDescriptor> attrList = shapefileDataStore.getSchema().getAttributeDescriptors();
                FeatureWriter<SimpleFeatureType, SimpleFeature> writer = createFeaClass2(shapefileDataStore, postgisDatasore, attrList, pgtableName.toLowerCase());
                SimpleFeatureStore pgFeatureDatastore = (SimpleFeatureStore) postgisDatasore.getFeatureSource(pgtableName.toLowerCase());

                if (writer != null) {
                    String jdbcUrl = this.getJDBCUrl(params);
                    String username = USER.lookUp(params).toString();
                    String password = PASSWD.lookUp(params).toString();

                    // 使用 try-with-resources 管理数据库连接和其他资源
                    try (Connection connection = DriverManager.getConnection(jdbcUrl, username, password)) {
                        String typeName = shapefileDataStore.getTypeNames()[0];
                        FeatureSource<SimpleFeatureType, SimpleFeature> featureSource = shapefileDataStore.getFeatureSource(typeName);
                        FeatureCollection<SimpleFeatureType, SimpleFeature> result = featureSource.getFeatures();

                        List<AttributeDescriptor> pgAttrList = pgFeatureDatastore.getSchema().getAttributeDescriptors();
                        String insertSql = getPgClassInsertSql2(pgtableName.toLowerCase(), pgAttrList);
                        try (PreparedStatement preparedStatement = connection.prepareStatement(insertSql);
                             FeatureIterator<SimpleFeature> iterator = result.features()) {

                            int index = 1;
                            int batchSize = 10000;
                            int count = 0;
                            WKTWriter wktWriter = new WKTWriter();

                            while (iterator.hasNext()) {
                                SimpleFeature feature = iterator.next();
                                Map<String, Object> mapValues = new LinkedHashMap<>();

                                for (Property prop : feature.getProperties()) {
                                    String fieldName = prop.getName().toString().toLowerCase();
                                    if ("the_geom".equals(fieldName)) {
                                        fieldName = "geom";
                                        Geometry geometry = (Geometry) prop.getValue();
                                        String wkt = wktWriter.write(geometry);
                                        mapValues.put(fieldName, wkt);
                                    } else {
                                        mapValues.put(fieldName, prop.getValue());
                                    }
                                }

                                mapValues.put("fid", index);

                                int paramIndex = 1;
                                for (String key : mapValues.keySet()) {
                                    if ("geom".equals(key)) {
                                        preparedStatement.setString(paramIndex++, (String) mapValues.get(key));
                                    } else {
                                        preparedStatement.setObject(paramIndex++, mapValues.get(key));
                                    }
                                }

                                preparedStatement.addBatch();
                                index++;
                                count++;

                                if (count % batchSize == 0) {
                                    log.info("Inserted {" + count + "} records.");
                                    preparedStatement.executeBatch();
                                    preparedStatement.clearBatch();
                                }
                            }

                            // 执行剩余的批处理
                            if (count % batchSize != 0) {
                                preparedStatement.executeBatch();
                            }

                            log.info("Successfully inserted {" + count + "} records into table {" + pgtableName + "}.");
                        }
                    } catch (SQLException e) {
                        log.error("Database operation failed.", e);
                        return false;
                    }

                    postgisDatasore.dispose();
                    return true;
                }
            }
        } catch (IOException e) {
            log.error("Failed to read shapefile.", e);
        }
        return false;
    }

    public FeatureWriter<SimpleFeatureType, SimpleFeature> createFeaClass2(ShapefileDataStore shapefileDataStore, DataStore dataStore, List<AttributeDescriptor> attrList, String strFeaClassName) {
        try {
            SimpleFeatureTypeBuilder tBuilder = new SimpleFeatureTypeBuilder();
            tBuilder.setName(strFeaClassName);

            SimpleFeatureType schema = shapefileDataStore.getSchema();
            CoordinateReferenceSystem dataCrs = schema.getCoordinateReferenceSystem();
            tBuilder.setCRS(dataCrs);

            for (AttributeDescriptor attributeDescriptor : attrList) {
                Class<?> classType = Class.forName(attributeDescriptor.getType().getBinding().getName());
                String attrName = attributeDescriptor.getName().toString().toLowerCase();

                if (Geometry.class.isAssignableFrom(classType)) {
                    tBuilder.add("geom", classType);
                } else if (Double.class.isAssignableFrom(classType) || Float.class.isAssignableFrom(classType)) {
                    tBuilder.add(attrName, BigDecimal.class);
                } else {
                    tBuilder.add(attrName, classType);
                }
            }

            dataStore.createSchema(tBuilder.buildFeatureType());
            return dataStore.getFeatureWriterAppend(strFeaClassName, Transaction.AUTO_COMMIT);
        } catch (Exception e) {
            log.error("Failed to create feature class.", e);
            return null;
        }
    }

    private String getPgClassInsertSql2(String tableName, List<AttributeDescriptor> pgAttrList) {
        List<String> columns = new ArrayList<>();
        List<String> values = new ArrayList<>();

        columns.add("fid"); // 假设 fid 是第一个字段
        values.add("?");

        for (AttributeDescriptor attr : pgAttrList) {
            String attrName = attr.getName().toString();
            columns.add(attrName);
            if ("geom".equals(attrName)) {
                values.add("ST_GeomFromText(?, 4326)");
            } else {
                values.add("?");
            }
        }

        return String.format("INSERT INTO %s (%s) VALUES (%s)", tableName, String.join(", ", columns), String.join(", ", values));
    }

    /**
     * 增量数据转换并导出为GeoJSON格式
     * 支持基于up_date字段的日期过滤和数据转换规则
     *
     * @param tableNames    要处理的表名数组，支持: link_m, node_m, relation_m, rule_m
     * @param geojsonpath   GeoJSON文件输出路径
     * @param startDate     开始日期 (格式: yyyy-MM-dd HH:mm:ss)
     * @param endDate       结束日期 (格式: yyyy-MM-dd HH:mm:ss)
     * @param excludeFields 要排除的字段数组（可选）
     * @return 处理结果
     */
    public boolean incrementalDataConvert2GeoJSON(String[] tableNames, String geojsonpath,String compressNamePrefix,
                                                  String startDate, String endDate, String[] excludeFields) {
        boolean result = false;
        List<Map<String, Object>> allTransformedData = new ArrayList<>();
        List<DataOutputDesc> dataOutputDescList = new ArrayList<>();
        MD5 md5 = new MD5();

        try {
            File geojsonPathFile = new File(geojsonpath);
            if (!geojsonPathFile.isDirectory()&&!geojsonPathFile.exists()) {
                geojsonPathFile.mkdir();
            }
            for (String tableName : tableNames) {
                if (!isValidTableName(tableName)) {
                    log.warn("跳过不支持的表名: " + tableName);
                    continue;
                }

                log.info("开始处理表: " + tableName);
                if ("link_e".equals(tableName)) {
                    excludeFields = new String[]{"link_old", "link_new", "olv", "tile_id", "tile_type", "task_id"};
                }
                if ("node_e".equals(tableName)) {
                    excludeFields = new String[]{"olv", "tile_id", "tile_type", "task_id"};
                }
                if ("relation_e".equals(tableName)) {
                    excludeFields = new String[]{"olv", "tile_id", "tile_type", "task_id"};
                }
                if ("rule_e".equals(tableName)) {
                    excludeFields = new String[]{"olv", "tile_id", "tile_type", "task_id"};
                }
                List<Map<String, Object>> tableData = processTableWithDateFilter(
                        tableName, startDate, endDate, excludeFields, dataOutputDescList);

                String handleGeojsonpath = geojsonpath.endsWith("/") ? geojsonpath + TableNameEnum.getTargetTableName(tableName) + ".geojson" : geojsonpath + "/" + TableNameEnum.getTargetTableName(tableName) + ".geojson";
                result = exportTransformedDataToGeoJSON(tableData, handleGeojsonpath);
                log.info("表 " + tableName + " 处理完成，获得 " + tableData.size() + " 条记录");
                if (!result) {
                    log.error("表 " + tableName + " 导出失败");
                    return false;
                } else {
                    String finalGeojsonpath = handleGeojsonpath;
                    dataOutputDescList.stream().filter(s -> s.getTable().equals(TableNameEnum.getTargetTableName(tableName)))
                                               .forEach(s -> s.setMd5(md5.digestHex(new File(finalGeojsonpath))));
                }

            }
            if (CollUtil.isNotEmpty(dataOutputDescList)) {
                // 导出csv
                String csvPath = generateCsvPath(geojsonpath,compressNamePrefix);
                boolean csvExportResult = exportDataOutputDescToCsv(dataOutputDescList, csvPath);
                if (csvExportResult) {
                    log.info("CSV导出成功，文件路径: " + csvPath);
                } else {
                    log.error("CSV导出失败");
                }
            }

            // if (!allTransformedData.isEmpty()) {
            //     // 将转换后的数据导出为GeoJSON
            //     result = exportTransformedDataToGeoJSON(allTransformedData, geojsonpath);
            //     // TODO
            //     MD5 md5 = new MD5();
            //     // md5.digestHex();
            //     log.info("总共处理了 " + allTransformedData.size() + " 条记录");
            // } else {
            //     log.warn("没有找到符合条件的数据");
            //     result = true; // 没有数据也算成功
            // }

        } catch (Exception e) {
            log.error("增量数据转换过程中发生错误: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 验证表名是否支持
     */
    private boolean isValidTableName(String tableName) {
        return "link_e".equals(tableName) || "node_e".equals(tableName) ||
                "relation_e".equals(tableName) || "rule_e".equals(tableName);
    }

    /**
     * 处理单个表的数据，应用日期过滤和数据转换
     */
    private List<Map<String, Object>> processTableWithDateFilter(String tableName, String startDate,
                                                                 String endDate, String[] excludeFields, List<DataOutputDesc> dataOutputDescList) {
        List<Map<String, Object>> transformedData = new ArrayList<>();

        try {
            // 构建日期过滤条件
            FilterFactory2 ff = CommonFactoryFinder.getFilterFactory2();
            Filter dateFilter = buildDateFilter(ff, startDate, endDate);

            // 获取表结构和数据
            SimpleFeatureSource featureSource = postgisDatasore.getFeatureSource(tableName);
            SimpleFeatureType schema = featureSource.getSchema();

            // 构建查询，包含日期过滤
            Query query = new Query(tableName, dateFilter);

            int total = featureSource.getCount(query);
            if (total == -1) {              // 若不支持 count
                CountVisitor cv = new CountVisitor();
                featureSource.getFeatures(query).accepts(cv, null);
                total = cv.getCount();
            }
            dataOutputDescList.add(DataOutputDesc.builder().table(TableNameEnum.getTargetTableName(tableName)).data_count(total).build());

            // 处理排除字段
            if (excludeFields != null && excludeFields.length > 0) {
                List<String> includeFields = getIncludeFields(schema, excludeFields);
                query.setPropertyNames(includeFields);
            }

            SimpleFeatureCollection featureCollection = featureSource.getFeatures(query);
            SimpleFeatureIterator iterator = featureCollection.features();

            while (iterator.hasNext()) {
                SimpleFeature feature = iterator.next();
                Map<String, Object> transformedRecord = transformFeatureData(tableName, feature);
                if (transformedRecord != null) {
                    transformedData.add(transformedRecord);
                }
            }
            iterator.close();

        } catch (Exception e) {
            log.error("处理表 " + tableName + " 时发生错误: " + e.getMessage(), e);
        }

        return transformedData;
    }

    /**
     * 构建日期过滤条件
     */
    private Filter buildDateFilter(FilterFactory2 ff, String startDate, String endDate) {
        try {
            Filter startFilter = ff.greaterOrEqual(ff.property("up_date"), ff.literal(startDate));
            Filter endFilter = ff.lessOrEqual(ff.property("up_date"), ff.literal(endDate));
            return ff.and(startFilter, endFilter);
        } catch (Exception e) {
            log.error("构建日期过滤条件失败: " + e.getMessage(), e);
            return Filter.INCLUDE; // 如果过滤条件构建失败，返回包含所有数据的过滤器
        }
    }

    /**
     * 获取包含的字段列表（排除指定字段）
     */
    private List<String> getIncludeFields(SimpleFeatureType schema, String[] excludeFields) {
        List<String> includeFields = new ArrayList<>();
        List<String> excludeList = Arrays.asList(excludeFields);

        for (AttributeDescriptor descriptor : schema.getAttributeDescriptors()) {
            String fieldName = descriptor.getLocalName();
            if (!excludeList.contains(fieldName)) {
                includeFields.add(fieldName);
            }
        }

        return includeFields;
    }

    /**
     * 根据表名转换要素数据
     */
    private Map<String, Object> transformFeatureData(String tableName, SimpleFeature feature) {
        Map<String, Object> transformedData = new HashMap<>();

        try {
            switch (tableName) {
                case "link_e":
                    return transformLinkMData(feature);
                case "node_e":
                    return transformNodeMData(feature);
                case "relation_e":
                    return transformRelationMData(feature);
                case "rule_e":
                    return transformRuleMData(feature);
                default:
                    log.warn("未知的表名: " + tableName);
                    return null;
            }
        } catch (Exception e) {
            log.error("转换数据时发生错误，表: " + tableName + ", 错误: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换link_m表数据到link模型
     */
    private Map<String, Object> transformLinkMData(SimpleFeature feature) {
        Map<String, Object> linkData = new HashMap<>();

        try {
            // 定义具有特殊映射规则的字段
            Set<String> mappedFields = new HashSet<>(Arrays.asList(
                    "link_id", "dir", "app", "devs", "spet", "funct", "urban", "pave",
                    "lane_n", "lane_l", "lane_r", "lane_c", "viad", "l_admin", "r_admin",
                    "f_speed", "t_speed", "sp_class", "dici_type", "t_admin", "time_zone"
            ));

            // 应用特殊字段映射规则
            linkData.put("id", getFeatureAttribute(feature, "link_id"));
            linkData.put("direction", getFeatureAttribute(feature, "dir"));
            linkData.put("const_st", getFeatureAttribute(feature, "app"));
            linkData.put("detailcity", getFeatureAttribute(feature, "devs"));
            linkData.put("special", getFeatureAttribute(feature, "spet"));
            linkData.put("funcclass", getFeatureAttribute(feature, "funct"));
            linkData.put("uflag", getFeatureAttribute(feature, "urban"));
            linkData.put("road_cond", getFeatureAttribute(feature, "pave"));
            linkData.put("lanenumsum", getFeatureAttribute(feature, "lane_n"));
            linkData.put("lanenums2e", getFeatureAttribute(feature, "lane_l"));
            linkData.put("lanenume2s", getFeatureAttribute(feature, "lane_r"));
            linkData.put("lanenumc", getFeatureAttribute(feature, "lane_c"));
            linkData.put("elevated", getFeatureAttribute(feature, "viad"));
            linkData.put("admincodel", getFeatureAttribute(feature, "l_admin"));
            linkData.put("admincoder", getFeatureAttribute(feature, "r_admin"));
            linkData.put("spdlmts2e", getFeatureAttribute(feature, "f_speed"));
            linkData.put("spdlmte2s", getFeatureAttribute(feature, "t_speed"));
            linkData.put("speedclass", getFeatureAttribute(feature, "sp_class"));
            linkData.put("dc_type", getFeatureAttribute(feature, "dici_type"));
            linkData.put("t_admin", getFeatureAttribute(feature, "t_admin"));
            linkData.put("time_zone", getFeatureAttribute(feature, "time_zone"));

            // 直接复制所有其他字段（没有特殊映射规则的字段）
            int copiedFieldsCount = 0;
            for (Property property : feature.getProperties()) {
                String propertyName = property.getName().getLocalPart();

                // 跳过几何字段（单独处理）和已经映射的字段
                if (!"geom".equals(propertyName) && !"geometry".equals(propertyName) &&
                        !mappedFields.contains(propertyName)) {
                    linkData.put(propertyName, property.getValue());
                    copiedFieldsCount++;
                }
            }

            log.debug("link_m表转换完成: 应用了21个特殊映射规则，直接复制了" + copiedFieldsCount + "个其他字段");

            // 保留几何信息
            linkData.put("geometry", feature.getDefaultGeometry());
            // linkData.put("table_source", "link_m");

        } catch (Exception e) {
            log.error("转换link_m数据时发生错误: " + e.getMessage(), e);
        }

        return linkData;
    }

    /**
     * 转换node_m表数据到node模型
     */
    private Map<String, Object> transformNodeMData(SimpleFeature feature) {
        Map<String, Object> nodeData = new HashMap<>();

        try {
            // 应用特定的字段映射
            nodeData.put("id", getFeatureAttribute(feature, "node_id"));
            nodeData.put("light_flag", getFeatureAttribute(feature, "light"));

            // 其他字段直接复制
            for (Property property : feature.getProperties()) {
                String propertyName = property.getName().getLocalPart();
                if (!"node_id".equals(propertyName) && !"light".equals(propertyName)) {
                    nodeData.put(propertyName, property.getValue());
                }
            }

            // nodeData.put("table_source", "node_m");

        } catch (Exception e) {
            log.error("转换node_m数据时发生错误: " + e.getMessage(), e);
        }

        return nodeData;
    }

    /**
     * 转换relation_m表数据到relation模型
     */
    private Map<String, Object> transformRelationMData(SimpleFeature feature) {
        Map<String, Object> relationData = new HashMap<>();

        try {
            // 过滤掉工作相关字段，直接复制其他字段
            for (Property property : feature.getProperties()) {
                String propertyName = property.getName().getLocalPart();
                if (!isWorkRelatedField(propertyName)) {
                    relationData.put(propertyName, property.getValue());
                }
            }

            // relationData.put("table_source", "relation_m");

        } catch (Exception e) {
            log.error("转换relation_m数据时发生错误: " + e.getMessage(), e);
        }

        return relationData;
    }

    /**
     * 转换rule_m表数据到rule模型
     */
    private Map<String, Object> transformRuleMData(SimpleFeature feature) {
        Map<String, Object> ruleData = new HashMap<>();

        try {
            // 过滤掉工作相关字段，直接复制其他字段
            for (Property property : feature.getProperties()) {
                String propertyName = property.getName().getLocalPart();
                if (!isWorkRelatedField(propertyName)) {
                    ruleData.put(propertyName, property.getValue());
                }
            }

            // ruleData.put("table_source", "rule_m");

        } catch (Exception e) {
            log.error("转换rule_m数据时发生错误: " + e.getMessage(), e);
        }

        return ruleData;
    }

    /**
     * 判断是否为工作相关字段
     */
    private boolean isWorkRelatedField(String fieldName) {
        // 定义工作相关字段列表，可以根据实际需要调整
        String[] workFields = {"work_id", "work_status", "work_type", "worker_id", "work_date"};
        return Arrays.asList(workFields).contains(fieldName.toLowerCase());
    }

    /**
     * 安全获取要素属性值
     */
    private Object getFeatureAttribute(SimpleFeature feature, String attributeName) {
        try {
            return feature.getAttribute(attributeName);
        } catch (Exception e) {
            log.warn("获取属性 " + attributeName + " 失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 将转换后的数据导出为GeoJSON格式
     */
    private boolean exportTransformedDataToGeoJSON(List<Map<String, Object>> transformedData, String geojsonpath) {
        try {
            // 创建GeoJSON结构
            Map<String, Object> geoJson = new HashMap<>();
            geoJson.put("type", "FeatureCollection");

            List<Map<String, Object>> features = new ArrayList<>();

            for (Map<String, Object> data : transformedData) {
                Map<String, Object> feature = new HashMap<>();
                feature.put("type", "Feature");

                // 处理几何信息
                Object geometry = data.get("geometry");
                if (geometry != null) {
                    feature.put("geometry", convertGeometryToGeoJSON(geometry));
                } else {
                    feature.put("geometry", null);
                }

                // 处理属性信息
                Map<String, Object> properties = new HashMap<>(data);
                properties.remove("geometry"); // 移除几何信息，因为已经单独处理
                feature.put("properties", properties);

                features.add(feature);
            }

            geoJson.put("features", features);

            // 写入文件
            ObjectMapper mapper = new ObjectMapper();
            mapper.enable(SerializationFeature.INDENT_OUTPUT);

            File outputFile = new File(geojsonpath);
            Utility.valiFileForWrite(geojsonpath);
            mapper.writeValue(outputFile, geoJson);

            log.info("GeoJSON文件导出成功: " + geojsonpath);
            return true;

        } catch (Exception e) {
            log.error("导出GeoJSON文件失败: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将几何对象转换为GeoJSON格式
     */
    private Object convertGeometryToGeoJSON(Object geometry) {
        try {
            if (geometry instanceof Geometry) {
                Geometry geom = (Geometry) geometry;
                GeometryJSON geometryJSON = new GeometryJSON(15);
                StringWriter writer = new StringWriter();
                geometryJSON.write(geom, writer);

                ObjectMapper mapper = new ObjectMapper();
                return mapper.readValue(writer.toString(), Object.class);
            }
        } catch (Exception e) {
            log.warn("转换几何对象失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 根据GeoJSON路径生成CSV文件路径
     *
     * @param geojsonPath GeoJSON文件路径
     * @return CSV文件路径
     */
    private String generateCsvPath(String geojsonPath,String compressNamePrefix) {
        String resOutputName = StrUtil.isNotBlank(compressNamePrefix)?compressNamePrefix:"data_output_summary";
        if (geojsonPath == null || geojsonPath.trim().isEmpty()) {
            return resOutputName + ".csv";
        }

        // 如果是目录路径，在目录下创建CSV文件
        if (geojsonPath.endsWith("/") || geojsonPath.endsWith("\\")) {
            return geojsonPath + resOutputName + ".csv";
        } else {
            return geojsonPath+ "/" + resOutputName + ".csv";
        }

        // 如果是文件路径，替换扩展名为.csv，或在同目录下创建CSV文件
        // File geojsonFile = new File(geojsonPath);
        // String parentDir = geojsonFile.getParent();
        // if (parentDir != null) {
        //     return parentDir + File.separator + resOutputName + ".csv";
        // } else {
        //     return resOutputName + ".csv";
        // }
    }

    /**
     * 将DataOutputDesc列表导出为CSV文件
     *
     * @param dataOutputDescList 数据输出描述列表
     * @param csvPath CSV文件路径
     * @return 导出是否成功
     */
    private boolean exportDataOutputDescToCsv(List<DataOutputDesc> dataOutputDescList, String csvPath) {
        if (dataOutputDescList == null || dataOutputDescList.isEmpty()) {
            log.warn("数据输出描述列表为空，跳过CSV导出");
            return true;
        }

        try {
            // 确保目录存在
            File csvFile = new File(csvPath);
            File parentDir = csvFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                boolean dirCreated = parentDir.mkdirs();
                if (!dirCreated) {
                    log.error("无法创建目录: " + parentDir.getAbsolutePath());
                    return false;
                }
            }

            // 使用Apache Commons CSV写入文件，确保UTF-8编码兼容性
            try (BufferedWriter bufferedWriter = Files.newBufferedWriter(csvFile.toPath(), StandardCharsets.UTF_8);
                 CSVPrinter csvPrinter = new CSVPrinter(bufferedWriter,
                     CSVFormat.DEFAULT.withHeader("Table", "Data Count", "MD5"))) {

                // 写入数据行
                for (DataOutputDesc desc : dataOutputDescList) {
                    csvPrinter.printRecord(
                        desc.getTable() != null ? desc.getTable() : "",
                        desc.getData_count() != null ? desc.getData_count() : 0,
                        desc.getMd5() != null ? desc.getMd5() : ""
                    );
                }

                csvPrinter.flush();
                log.info("成功导出 " + dataOutputDescList.size() + " 条记录到CSV文件: " + csvPath);
                return true;

            }
        } catch (IOException e) {
            log.error("导出CSV文件时发生IO错误: " + e.getMessage(), e);
            return false;
        } catch (Exception e) {
            log.error("导出CSV文件时发生未知错误: " + e.getMessage(), e);
            return false;
        }
    }
}
