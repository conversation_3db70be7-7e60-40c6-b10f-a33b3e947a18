package com.hll.dataconvertservice.gts4vect;


import org.apache.log4j.Logger;
import org.geotools.data.*;
import org.geotools.data.postgis.PostgisNGDataStoreFactory;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.*;

/**
 */
public class App {

    public static void main(String[] args) throws IOException {

        PGDatastore pgDatastore = new PGDatastore();
        Map<String, Object> params = new HashMap<>();
        params.put(PostgisNGDataStoreFactory.DBTYPE.key, "postgis");
        params.put(PostgisNGDataStoreFactory.HOST.key, "**************");
        params.put(PostgisNGDataStoreFactory.PORT.key,15999);
        params.put(PostgisNGDataStoreFactory.DATABASE.key , "hll_oversea_h_vnm_2022_q2");
        params.put(PostgisNGDataStoreFactory.SCHEMA.key, "public");
        params.put(PostgisNGDataStoreFactory.USER.key, "postgres");
        params.put(PostgisNGDataStoreFactory.PASSWD.key, "Huolala@2021");

        DataStore dataStore = DataStoreFinder.getDataStore(params);
        //DataStore datastore = PGDatastore.getDefeaultDatastore();

        String geojsonpath = "C:\\test\\ChinaWorldCitysBigbelin\\chinaCompany2.geojson";
        String shpfilepath = "/Users/<USER>/Downloads/JNCN211F0WJN000AACVH/J2AM211F0WJ2000AACVH/J2AM211F0WJ2000AACVH/Streets.shp";
        String pgtableName = "Streets_test3";
        Charset shpCharset = Charset.forName("utf-8");

        Geotools geotools = new Geotools(dataStore);
//        geotools.shp2pgInjector(params,shpfilepath,pgtableName,shpCharset);
        geotools.pgtable2shp("poi_src","/Users/<USER>/Downloads/poi.shp","poi_geo");
        geotools.pgtable2geojson("poi","/Users/<USER>/Downloads/poi.geojson");

//        geotools.geojson2pgtable(geojsonpath, pgtableName);
//        geotools.geojson2shp(geojsonpath, shpfilepath);
//        geotools.shp2geojson(shpfilepath, geojsonpath);
//        geotools.shp2pgtable(shpfilepath, pgtableName);
//        utility.tagLast("shp导入postgis");

//        geotools.pgtable2geojson(pgtableName, geojsonpath);
//        geotools.pgtable2shp(pgtableName, shpfilepath, "geom");


    }

    private static Logger logger = Logger.getLogger(App.class);
    private static Utility utility = new Utility();
}
