package com.hll.dataconvertservice.gts4vect;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import org.apache.commons.io.Charsets;
import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;
import org.geotools.data.DataStore;
import org.geotools.data.collection.ListFeatureCollection;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureIterator;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.geojson.feature.FeatureJSON;
import org.geotools.geojson.geom.GeometryJSON;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.feature.simple.SimpleFeatureType;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/13
 */
public class Geotools2 {
    private static Logger logger = Logger.getLogger(Geotools.class);
    private static DataStore postgisDatasore;

    public Geotools2(DataStore postgisDatasore) {
        if (postgisDatasore == null) {
            postgisDatasore = PGDatastore.getDefeaultDatastore();
        }
        Geotools2.postgisDatasore = postgisDatasore;
    }

    public Geotools2() {
        postgisDatasore = PGDatastore.getDefeaultDatastore();
    }


    public boolean pgtable2geojson(String pgtableName, String geojsonpath, String pidName) {
        boolean result = false;
        try {
            SimpleFeatureSource featureSource = postgisDatasore.getFeatureSource(pgtableName);
            SimpleFeatureCollection featureCollection = featureSource.getFeatures();
            FeatureJSON featureJSON = new FeatureJSON(new GeometryJSON(15));
            //允许空值字段带出
            featureJSON.setEncodeNullValues(true);
            int size = featureCollection.size();
            int batch = 200000;
            int batchNum = 0;
            boolean isWriteRest = true;
            if(size % batch == 0){
                batchNum = size / batch;
                isWriteRest = false;
            }
            System.out.println("process start. "+ size + " records to process");
            File outputJsonFile = new File(geojsonpath);
           FileUtils.writeStringToFile(outputJsonFile, "{\n\"type\":\"FeatureCollection\",\n\"features\":[\n", Charsets.toCharset("utf-8"), true);
            // FileUtils.writeStringToFile(outputJsonFile, "{\"type\":\"FeatureCollection\",\"features\":[", Charsets.toCharset("utf-8"), true);
            List<SimpleFeature> featureList = new ArrayList<>();
            // 拿到迭代器
            SimpleFeatureIterator featureIterator = featureCollection.features();
            // 遍历每一个要素
            int i = 0;
            int j = 0;
            SimpleFeatureType simpleFeatureType =null;
            while(featureIterator.hasNext()) {
                i++;
                SimpleFeature feature = featureIterator.next();
                if(i==1){
                    simpleFeatureType = feature.getFeatureType();
                    System.out.println("feature type is: " + simpleFeatureType);
                }
//                if(i > 2400000){
                featureList.add(feature);
//                }
                if(i % batch == 0){
                    j++;
//                    if(j == 5){
                    SimpleFeatureCollection collection = new ListFeatureCollection(simpleFeatureType,featureList);
                    String s = featureJSON.toString(collection);
                    String results = processPid(s, pidName);
                    FileUtils.writeStringToFile(outputJsonFile, results, Charsets.toCharset("utf-8"), true);
                    if (j != batchNum){
                        FileUtils.writeStringToFile(outputJsonFile, ",", Charsets.toCharset("utf-8"), true);
                    } else {
                        FileUtils.writeStringToFile(outputJsonFile, "\n]\n}", Charsets.toCharset("utf-8"), true);
                    }
//                    }
                    featureList.clear();
                    System.out.println("processed " + i +" of "+size);
                }
            }
            if(isWriteRest){
                System.out.println("process the last  " + featureList.size() + " records");
                System.out.println("feature type is: " + simpleFeatureType);
                SimpleFeatureCollection collection = new ListFeatureCollection(simpleFeatureType,featureList);
                System.out.println("last collection size is: " + collection.size());
                String s = featureJSON.toString(collection);
                String results = processPid(s,pidName);
                FileUtils.writeStringToFile(outputJsonFile, results, Charsets.toCharset("utf-8"), true);
               FileUtils.writeStringToFile(outputJsonFile, "\n]\n}", Charsets.toCharset("utf-8"), true);
                // FileUtils.writeStringToFile(outputJsonFile, "]}", Charsets.toCharset("utf-8"), true);
            }
            System.out.println("process finished");
            result = true;

        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return result;
    }

    ValueFilter filter = new ValueFilter() {
        @Override
        public Object process(Object object, String name, Object value) {
            if (value instanceof BigDecimal || value instanceof Double || value instanceof Float) {
                return new BigDecimal(value.toString());
            }
            if("LineString".equals(((JSONObject) object).get("type")))
            {
                if (name.equals("coordinates")){
                    JSONArray coordinates = (JSONArray)value;
                    for (int j = 0; j < coordinates.size(); j++) {
                        JSONArray jsonArray1 = coordinates.getJSONArray(j);
                        for (int m = 0; m < jsonArray1.size(); m++) {
                            jsonArray1.set(m, jsonArray1.getDouble(m));
                        }
                    }
                }
            }
            if("MultiLineString".equals(((JSONObject) object).get("type"))){
                if (name.equals("coordinates")){
                    JSONArray coordinates = (JSONArray)value;
                    JSONArray jsonArray = (JSONArray) coordinates.get(0);
                    for (int j = 0; j < jsonArray.size(); j++) {
                        JSONArray jsonArray1 = jsonArray.getJSONArray(j);
                        for (int m = 0; m < jsonArray1.size(); m++) {
                            jsonArray1.set(m, jsonArray1.getDouble(m));
                        }
                    }
                }
            }

            return value;
        }
    };
    public String processPid(String s,String pidName) {
        JSONObject sJson = JSONObject.parseObject(s);
        JSONArray features = sJson.getJSONArray("features");
        for (int k = 0; k < features.size(); k++)
        {
            JSONObject featureOne = features.getJSONObject(k);
            JSONObject properties = featureOne.getJSONObject("properties");
            String futureId = featureOne.get("id").toString();
            String pid = futureId.split("\\.")[1];
            //移除id，propteries增加主键id
            featureOne.remove("id");
            properties.put(pidName,pid);
        }
        return JSONObject.toJSONString(sJson, filter, SerializerFeature.WriteMapNullValue)
                .replace("{\"features\":[","")
                 // .replace("],\"type\":\"FeatureCollection\"}","");
               .replace("],\"type\":\"FeatureCollection\"}","").replaceAll("},\\{","},\n{");
    }
}
