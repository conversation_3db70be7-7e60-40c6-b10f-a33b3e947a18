// License: GPL. For details, see LICENSE file.
package com.hll.dataconvertservice.gts4vect.osm;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.locks.Lock;

/**
 * Abstraction of {@link DataSet}.
 * This class holds OSM data but does not rely on implementation types,
 * allowing plugins to define their own representation of OSM data if needed.
 * @param <O> the base type of OSM primitives
 * @param <N> type representing OSM nodes
 * @param <W> type representing OSM ways
 * @param <R> type representing OSM relations
 * @since 13764
 */
public interface OsmData<O extends IPrimitive, N extends INode, W extends IWay<N>, R extends IRelation<?>> extends Lockable {

    // --------------
    //    Metadata
    // --------------

    /**
     * Replies the API version this dataset was created from. May be null.
     * @return the API version this dataset was created from. May be null.
     */
    String getVersion();

    /**
     * Returns the name of this data set (optional).
     * @return the name of this data set. Can be {@code null}
     * @since 12718
     */
    String getName();

    /**
     * Sets the name of this data set.
     * @param name the new name of this data set. Can be {@code null} to reset it
     * @since 12718
     */
    void setName(String name);

    // --------------------
    //    OSM primitives
    // --------------------

    /**
     * Adds a primitive.
     * @param primitive the primitive
     */
    void addPrimitive(O primitive);

    /**
     * Removes all primitives.
     */
    void clear();

    /**
     * Searches for nodes in the given bounding box.
     * @param bbox the bounding box
     * @return List of nodes in the given bbox. Can be empty but not null
     */
    List<N> searchNodes(BBox bbox);

    /**
     * Determines if the given node can be retrieved in the data set through its bounding box. Useful for dataset consistency test.
     * @param n The node to search
     * @return {@code true} if {@code n} can be retrieved in this data set, {@code false} otherwise
     */
    boolean containsNode(N n);

    /**
     * Searches for ways in the given bounding box.
     * @param bbox the bounding box
     * @return List of ways in the given bbox. Can be empty but not null
     */
    List<W> searchWays(BBox bbox);

    /**
     * Determines if the given way can be retrieved in the data set through its bounding box. Useful for dataset consistency test.
     * @param w The way to search
     * @return {@code true} if {@code w} can be retrieved in this data set, {@code false} otherwise
     */
    boolean containsWay(W w);

    /**
     * Searches for relations in the given bounding box.
     * @param bbox the bounding box
     * @return List of relations in the given bbox. Can be empty but not null
     */
    List<R> searchRelations(BBox bbox);

    /**
     * Determines if the given relation can be retrieved in the data set through its bounding box. Useful for dataset consistency test.
     * @param r The relation to search
     * @return {@code true} if {@code r} can be retrieved in this data set, {@code false} otherwise
     */
    boolean containsRelation(R r);

    /**
     * Returns a primitive with a given id from the data set. null, if no such primitive exists
     *
     * @param id uniqueId of the primitive. Might be &lt; 0 for newly created primitives
     * @param type the type of  the primitive. Must not be null.
     * @return the primitive
     * @throws NullPointerException if type is null
     */
    default O getPrimitiveById(long id, OsmPrimitiveType type) {
        return getPrimitiveById(new SimplePrimitiveId(id, type));
    }

    /**
     * Returns a primitive with a given id from the data set. null, if no such primitive exists
     *
     * @param primitiveId type and uniqueId of the primitive. Might be &lt; 0 for newly created primitives
     * @return the primitive
     */
    O getPrimitiveById(PrimitiveId primitiveId);

    /**
     * Replies an unmodifiable collection of nodes in this dataset
     *
     * @return an unmodifiable collection of nodes in this dataset
     */
    Collection<N> getNodes();

    /**
     * Replies an unmodifiable collection of ways in this dataset
     *
     * @return an unmodifiable collection of ways in this dataset
     */
    Collection<W> getWays();

    /**
     * Replies an unmodifiable collection of relations in this dataset
     *
     * @return an unmodifiable collection of relations in this dataset
     */
    Collection<R> getRelations();

    // --------------
    //    Policies
    // --------------

    /**
     * Get the download policy.
     * @return the download policy
     * @see #setDownloadPolicy(DownloadPolicy)
     * @since 13453
     */
    DownloadPolicy getDownloadPolicy();

    /**
     * Sets the download policy.
     * @param downloadPolicy the download policy. Must not be null
     * @see #getUploadPolicy()
     * @since 13453
     */
    void setDownloadPolicy(DownloadPolicy downloadPolicy);

    /**
     * Get the upload policy.
     * @return the upload policy
     * @see #setUploadPolicy(UploadPolicy)
     */
    UploadPolicy getUploadPolicy();

    /**
     * Sets the upload policy.
     * @param uploadPolicy the upload policy. Must not be null
     * @see #getUploadPolicy()
     */
    void setUploadPolicy(UploadPolicy uploadPolicy);

    // --------------
    //    Locks
    // --------------

    /**
     * Returns the lock used for reading.
     * @return the lock used for reading
     */
    Lock getReadLock();
    /**
     * Replies true if there is at least one primitive in this dataset with
     * {@link IPrimitive#isModified()} == <code>true</code>.
     *
     * @return true if there is at least one primitive in this dataset with
     * {@link IPrimitive#isModified()} == <code>true</code>.
     */
    default boolean isModified() {
        return false;
    }
}
