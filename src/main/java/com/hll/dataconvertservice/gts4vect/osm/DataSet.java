// License: GPL. For details, see LICENSE file.
package com.hll.dataconvertservice.gts4vect.osm;


import com.hll.dataconvertservice.gts4vect.SubclassFilteredCollection;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;


/**
 * DataSet is the data behind the application. It can consists of only a few points up to the whole
 * osm database. DataSet's can be merged together, saved, (up/down/disk)loaded etc.
 *
 * Note that DataSet is not an osm-primitive and so has no key association but a few members to
 * store some information.
 *
 * Dataset is threadsafe - accessing Dataset simultaneously from different threads should never
 * lead to data corruption or ConcurrentModificationException. However when for example one thread
 * removes primitive and other thread try to add another primitive referring to the removed primitive,
 * DataIntegrityException will occur.
 *
 * To prevent such situations, read/write lock is provided. While read lock is used, it's guaranteed that
 * Dataset will not change. Sample usage:
 * <code>
 *   ds.getReadLock().lock();
 *   try {
 *     // .. do something with dataset
 *   } finally {
 *     ds.getReadLock().unlock();
 *   }
 * </code>
 *
 * Write lock should be used in case of bulk operations. In addition to ensuring that other threads can't
 * use dataset in the middle of modifications it also stops sending of dataset events. That's good for performance
 * reasons - GUI can be updated after all changes are done.
 * Sample usage:
 * <code>
 * ds.beginUpdate()
 * try {
 *   // .. do modifications
 * } finally {
 *  ds.endUpdate();
 * }
 * </code>
 *
 * Note that it is not necessary to call beginUpdate/endUpdate for every dataset modification - dataset will get locked
 * automatically.
 *
 * Note that locks cannot be upgraded - if one threads use read lock and and then write lock, dead lock will occur - see #5814 for
 * sample ticket
 *
 * <AUTHOR>
 */
public final class DataSet implements OsmData<OsmPrimitive, Node, Way, Relation> {

    /**
     * Maximum number of events that can be fired between beginUpdate/endUpdate to be send as single events (ie without DatasetChangedEvent)
     */
    private static final int MAX_SINGLE_EVENTS = 30;

    /**
     * Maximum number of events to kept between beginUpdate/endUpdate. When more events are created, that simple DatasetChangedEvent is sent)
     */
    private static final int MAX_EVENTS = 1000;

    private final QuadBucketPrimitiveStore<Node, Way, Relation> store = new QuadBucketPrimitiveStore<>();

    private final Storage<OsmPrimitive> allPrimitives = new Storage<>(new Storage.PrimitiveIdHash(), true);
    private final Map<PrimitiveId, OsmPrimitive> primitivesMap = allPrimitives
            .foreignKey(new Storage.PrimitiveIdHash());

    // Number of open calls to beginUpdate
    private int updateCount;
    // Events that occurred while dataset was locked but should be fired after write lock is released

    private String name;
    private DownloadPolicy downloadPolicy = DownloadPolicy.NORMAL;
    private UploadPolicy uploadPolicy = UploadPolicy.NORMAL;
    /** Flag used to know if the dataset should not be editable */
    private final AtomicBoolean isReadOnly = new AtomicBoolean(false);

    private final ReadWriteLock lock = new ReentrantReadWriteLock();


    private short mappaintCacheIdx = 1;
    private String remark;

    /**
     * Constructs a new {@code DataSet}.
     */
    public DataSet() {
        // Transparently register as projection change listener. No need to explicitly remove
        // the listener, projection change listeners are managed as WeakReferences.
    }

    /**
     * Creates a new {@link DataSet}.
     * @param copyFrom An other {@link DataSet} to copy the contents of this dataset from.
     * @since 10346
     */
    public DataSet(DataSet copyFrom) {
        this();
        copyFrom.getReadLock().lock();
        try {
            clonePrimitives(copyFrom.getNodes(), copyFrom.getWays(), copyFrom.getRelations());
            version = copyFrom.version;
            uploadPolicy = copyFrom.uploadPolicy;
            downloadPolicy = copyFrom.downloadPolicy;
            isReadOnly.set(copyFrom.isReadOnly.get());
        } finally {
            copyFrom.getReadLock().unlock();
        }
    }

    /**
     * Constructs a new {@code DataSet} initially filled with the given primitives.
     * @param osmPrimitives primitives to add to this data set
     * @since 12726
     */
    public DataSet(OsmPrimitive... osmPrimitives) {
        this();
        update(() -> {
            for (OsmPrimitive o : osmPrimitives) {
                addPrimitive(o);
            }
        });
    }



    /**
     * Clones the specified primitives into this data set.
     * @param nodes nodes to clone
     * @param ways ways to clone
     * @param relations relations to clone
     * @return the map of cloned primitives indexed by their original version
     * @since 18001
     */
    public Map<OsmPrimitive, OsmPrimitive> clonePrimitives(Iterable<Node> nodes, Iterable<Way> ways, Iterable<Relation> relations) {
        Map<OsmPrimitive, OsmPrimitive> primMap = new HashMap<>();
        for (Node n : nodes) {
            Node newNode = new Node(n);
            primMap.put(n, newNode);
            addPrimitive(newNode);
        }
        for (Way w : ways) {
            Way newWay = new Way(w, false, false);
            primMap.put(w, newWay);
            List<Node> newNodes = w.getNodes().stream()
                    .map(n -> (Node) primMap.get(n))
                    .collect(Collectors.toList());
            newWay.setNodes(newNodes);
            addPrimitive(newWay);
        }
        // Because relations can have other relations as members we first clone all relations
        // and then get the cloned members
        for (Relation r : relations) {
            Relation newRelation = new Relation(r, false, false);
            primMap.put(r, newRelation);
            addPrimitive(newRelation);
        }
        for (Relation r : relations) {
            ((Relation) primMap.get(r)).setMembers(r.getMembers().stream()
                    .map(rm -> new RelationMember(rm.getRole(), primMap.get(rm.getMember())))
                    .collect(Collectors.toList()));
        }
        return primMap;
    }

    @Override
    public Lock getReadLock() {
        return lock.readLock();
    }

    public <T extends OsmPrimitive> Collection<T> getPrimitives(Predicate<? super OsmPrimitive> predicate) {
        return new SubclassFilteredCollection<>(allPrimitives, predicate);
    }

    @Override
    public Collection<Node> getNodes() {
        return getPrimitives(Node.class::isInstance);
    }

    @Override
    public Collection<Way> getWays() {
        return getPrimitives(Way.class::isInstance);
    }

    @Override
    public Collection<Relation> getRelations() {
        return getPrimitives(Relation.class::isInstance);
    }

    /**
     * History of selections - shared by plugins and SelectionListDialog
     */
    private final LinkedList<Collection<? extends OsmPrimitive>> selectionHistory = new LinkedList<>();

    /**
     * Replies the history of JOSM selections
     *
     * @return list of history entries
     */
    public LinkedList<Collection<? extends OsmPrimitive>> getSelectionHistory() {
        return selectionHistory;
    }

    /**
     * Clears selection history list
     */
    public void clearSelectionHistory() {
        selectionHistory.clear();
    }

    /**
     * The API version that created this data set, if any.
     */
    private String version;

    @Override
    public String getVersion() {
        return version;
    }

    /**
     * Sets the API version this dataset was created from.
     *
     * @param version the API version, i.e. "0.6"
     * @throws IllegalStateException if the dataset is read-only
     */
    public void setVersion(String version) {
        checkModifiable();
        this.version = version;
    }

    @Override
    public DownloadPolicy getDownloadPolicy() {
        return this.downloadPolicy;
    }

    @Override
    public void setDownloadPolicy(DownloadPolicy downloadPolicy) {
        this.downloadPolicy = Objects.requireNonNull(downloadPolicy);
    }

    @Override
    public UploadPolicy getUploadPolicy() {
        return this.uploadPolicy;
    }

    @Override
    public void setUploadPolicy(UploadPolicy uploadPolicy) {
        this.uploadPolicy = Objects.requireNonNull(uploadPolicy);
    }

    /**
     * Holding bin for changeset tag information, to be applied when or if this is ever uploaded.
     */
    private final Map<String, String> changeSetTags = new HashMap<>();

    /**
     * Replies the set of changeset tags to be applied when or if this is ever uploaded.
     * @return the set of changeset tags
     * @see #addChangeSetTag
     */
    public Map<String, String> getChangeSetTags() {
        return changeSetTags;
    }

    /**
     * Adds a new changeset tag.
     * @param k Key
     * @param v Value
     * @see #getChangeSetTags
     */
    public void addChangeSetTag(String k, String v) {
        if (v != null) {
            this.changeSetTags.put(k, v);
        } else {
            this.changeSetTags.remove(k);
        }
    }

    @Override
    public List<Node> searchNodes(BBox bbox) {
        lock.readLock().lock();
        try {
            return store.searchNodes(bbox);
        } finally {
            lock.readLock().unlock();
        }
    }

    @Override
    public List<Way> searchWays(BBox bbox) {
        lock.readLock().lock();
        try {
            return store.searchWays(bbox);
        } finally {
            lock.readLock().unlock();
        }
    }


    @Override
    public List<Relation> searchRelations(BBox bbox) {
        lock.readLock().lock();
        try {
            return store.searchRelations(bbox);
        } finally {
            lock.readLock().unlock();
        }
    }


    /**
     * Searches for all primitives in the given bounding box
     *
     * @param bbox the bounding box
     * @return List of primitives in the given bbox. Can be empty but not null
     * @since 15891
     */
    public List<OsmPrimitive> searchPrimitives(BBox bbox) {
        List<OsmPrimitive> primitiveList = new ArrayList<>();
        primitiveList.addAll(searchNodes(bbox));
        primitiveList.addAll(searchWays(bbox));
        primitiveList.addAll(searchRelations(bbox));
        return primitiveList;
    }


    /**
     * Determines if the given node can be retrieved in the data set through its bounding box. Useful for dataset consistency test.
     * For efficiency reasons this method does not lock the dataset, you have to lock it manually.
     *
     * @param n The node to search
     * @return {@code true} if {@code n} can be retrieved in this data set, {@code false} otherwise
     * @since 7501
     */
    @Override
    public boolean containsNode(Node n) {
        return store.containsNode(n);
    }

    /**
     * Determines if the given way can be retrieved in the data set through its bounding box. Useful for dataset consistency test.
     * For efficiency reasons this method does not lock the dataset, you have to lock it manually.
     *
     * @param w The way to search
     * @return {@code true} if {@code w} can be retrieved in this data set, {@code false} otherwise
     * @since 7501
     */
    @Override
    public boolean containsWay(Way w) {
        return store.containsWay(w);
    }

    /**
     * Determines if the given relation can be retrieved in the data set through its bounding box. Useful for dataset consistency test.
     * For efficiency reasons this method does not lock the dataset, you have to lock it manually.
     *
     * @param r The relation to search
     * @return {@code true} if {@code r} can be retrieved in this data set, {@code false} otherwise
     * @since 7501
     */
    @Override
    public boolean containsRelation(Relation r) {
        return store.containsRelation(r);
    }

    /**
     * Adds a primitive to the dataset.
     *
     * @param primitive the primitive.
     * @throws IllegalStateException if the dataset is read-only
     */
    @Override
    public void addPrimitive(OsmPrimitive primitive) {
        Objects.requireNonNull(primitive, "primitive");
        checkModifiable();
        update(() -> {

            allPrimitives.add(primitive);
            primitive.setDataset(this);
            primitive.updatePosition(); // Set cached bbox for way and relation (required for reindexWay and reindexRelation to work properly)
            store.addPrimitive(primitive);
        });
    }

    /**
     * Adds recursively a primitive, and all its children, to the dataset.
     *
     * @param primitive the primitive.
     * @throws IllegalStateException if the dataset is read-only
     * @since 17981
     */
    public void addPrimitiveRecursive(OsmPrimitive primitive) {
        if (primitive instanceof Way) {
            ((Way) primitive).getNodes().forEach(n -> addPrimitiveRecursive(n));
        } else if (primitive instanceof Relation) {
            ((Relation) primitive).getMembers().forEach(m -> addPrimitiveRecursive(m.getMember()));
        }
        addPrimitive(primitive);
    }

    /**
     * Removes a primitive from the dataset. This method only removes the
     * primitive form the respective collection of primitives managed
     * by this dataset, i.e. from {@code store.nodes}, {@code store.ways}, or
     * {@code store.relations}. References from other primitives to this
     * primitive are left unchanged.
     *
     * @param primitiveId the id of the primitive
     * @throws IllegalStateException if the dataset is read-only
     */
    public void removePrimitive(PrimitiveId primitiveId) {
        checkModifiable();
        update(() -> {
            OsmPrimitive primitive = getPrimitiveByIdChecked(primitiveId);
            if (primitive == null) {
                return;
            }
            removePrimitiveImpl(primitive);
        });
    }

    private void removePrimitiveImpl(OsmPrimitive primitive) {
        if (primitive.isSelected()) {
            //throw new DataIntegrityProblemException("Primitive was re-selected by a selection listener: " + primitive);
        }
        store.removePrimitive(primitive);
        allPrimitives.remove(primitive);
        primitive.setDataset(null);
    }

    void removePrimitive(OsmPrimitive primitive) {
        checkModifiable();
        update(() -> {
            removePrimitiveImpl(primitive);
        });
    }
    @Override
    public OsmPrimitive getPrimitiveById(PrimitiveId primitiveId) {
        return primitiveId != null ? primitivesMap.get(primitiveId) : null;
    }

    /**
     * Show message and stack trace in log in case primitive is not found
     * @param primitiveId primitive id to look for
     * @return Primitive by id.
     */
    private OsmPrimitive getPrimitiveByIdChecked(PrimitiveId primitiveId) {
        OsmPrimitive result = getPrimitiveById(primitiveId);
        if (result == null && primitiveId != null) {
        }

        return result;
    }

    private static void deleteWay(Way way) {
        way.setNodes(null);
        way.setDeleted(true);
    }

    /**
     * Removes all references from ways in this dataset to a particular node.
     *
     * @param node the node
     * @return The set of ways that have been modified
     * @throws IllegalStateException if the dataset is read-only
     */
    public Set<Way> unlinkNodeFromWays(Node node) {
        checkModifiable();
        return update(() -> {
            Set<Way> result = new HashSet<>();
            for (Way way : node.getParentWays()) {
                List<Node> wayNodes;
                if (!way.isIncomplete()) {
                    wayNodes = way.calculateRemoveNodes(Collections.singleton(node));
                } else {
                    wayNodes = way.getNodes();
                    wayNodes.removeIf(node::equals);
                }
                if (wayNodes.size() < way.getNodesCount()) {
                    if (wayNodes.size() < 2) {
                        deleteWay(way);
                    } else {
                        way.setNodes(wayNodes);
                    }
                    result.add(way);
                }
            }
            return result;
        });
    }

    /**
     * removes all references from relations in this dataset  to this primitive
     *
     * @param primitive the primitive
     * @return The set of relations that have been modified
     * @throws IllegalStateException if the dataset is read-only
     */
    public Set<Relation> unlinkPrimitiveFromRelations(OsmPrimitive primitive) {
        checkModifiable();
        return update(() -> {
            Set<Relation> result = new HashSet<>();
            for (Relation relation : getRelations()) {
                List<RelationMember> members = relation.getMembers();
                boolean removed = members.removeIf(member -> member.getMember().equals(primitive));
                if (removed) {
                    relation.setMembers(members);
                    result.add(relation);
                }
            }
            return result;
        });
    }

    /**
     * Removes all references from other primitives to the referenced primitive.
     *
     * @param referencedPrimitive the referenced primitive
     * @return The set of primitives that have been modified
     * @throws IllegalStateException if the dataset is read-only
     */
    public Set<OsmPrimitive> unlinkReferencesToPrimitive(OsmPrimitive referencedPrimitive) {
        checkModifiable();
        return update(() -> {
            Set<OsmPrimitive> result = new HashSet<>();
            if (referencedPrimitive instanceof Node) {
                result.addAll(unlinkNodeFromWays((Node) referencedPrimitive));
            }
            result.addAll(unlinkPrimitiveFromRelations(referencedPrimitive));
            return result;
        });
    }

    @Override
    public boolean isModified() {
        return allPrimitives.parallelStream().anyMatch(OsmPrimitive::isModified);
    }

    /**
     * Can be called before bigger changes on dataset. Events are disabled until {@link #endUpdate()}.
     * <br>
     * Typical use case should look like this:
     * <pre>
     * ds.beginUpdate();
     * try {
     *   ...
     * } finally {
     *   ds.endUpdate();
     * }
     * </pre>
     * @see #endUpdate()
     */
    public void beginUpdate() {
        lock.writeLock().lock();
        updateCount++;
    }

    /**
     * Must be called after a previous call to {@link #beginUpdate()} to fire change events.
     * <br>
     * Typical use case should look like this:
     * <pre>
     * ds.beginUpdate();
     * try {
     *   ...
     * } finally {
     *   ds.endUpdate();
     * }
     * </pre>
     * @see DataSet#beginUpdate()
     */
    public void endUpdate() {
        if (updateCount > 0) {
//            updateCount--;
//            List<AbstractDatasetChangedEvent> eventsToFire = Collections.emptyList();
//            if (updateCount == 0) {
//                eventsToFire = new ArrayList<>(cachedEvents);
//                cachedEvents.clear();
//            }
//
//            if (!eventsToFire.isEmpty()) {
//                lock.readLock().lock();
//                try {
//                    lock.writeLock().unlock();
//                    if (eventsToFire.size() < MAX_SINGLE_EVENTS) {
//                        for (AbstractDatasetChangedEvent event : eventsToFire) {
//                            fireEventToListeners(event);
//                        }
//                    } else if (eventsToFire.size() == MAX_EVENTS) {
//                        fireEventToListeners(new DataChangedEvent(this));
//                    } else {
//                        fireEventToListeners(new DataChangedEvent(this, eventsToFire));
//                    }
//                } finally {
//                    lock.readLock().unlock();
//                }
//            } else {
//                lock.writeLock().unlock();
//            }

        } else {
            throw new AssertionError("endUpdate called without beginUpdate");
        }
    }

    /**
     * Performs the update runnable between {@link #beginUpdate()} / {@link #endUpdate()} calls.
     * @param runnable update action
     * @since 16187
     */
    public void update(Runnable runnable) {
        beginUpdate();
        try {
            runnable.run();
        } finally {
            endUpdate();
        }
    }

    /**
     * Performs the update function between {@link #beginUpdate()} / {@link #endUpdate()} calls.
     * @param function update function
     * @param t function argument
     * @param <T> argument type
     * @param <R> result type
     * @return function result
     * @since 16187
     */
    public <T, R> R update(Function<T, R> function, T t) {
        beginUpdate();
        try {
            return function.apply(t);
        } finally {
            endUpdate();
        }
    }

    /**
     * Performs the update supplier between {@link #beginUpdate()} / {@link #endUpdate()} calls.
     * @param supplier update supplier
     * @param <R> result type
     * @return supplier result
     * @since 16187
     */
    public <R> R update(Supplier<R> supplier) {
        beginUpdate();
        try {
            return supplier.get();
        } finally {
            endUpdate();
        }
    }

    /**
     * Cleanups all deleted primitives (really delete them from the dataset).
     */
    public void cleanupDeletedPrimitives() {
//        update(() -> {
//            Collection<OsmPrimitive> toCleanUp = getPrimitives(
//                    primitive -> primitive.isDeleted() && (!primitive.isVisible() || primitive.isNew()));
//            if (!toCleanUp.isEmpty()) {
//                // We unselect them in advance to not fire a selection change for every primitive
//                clearSelection(toCleanUp.stream().map(OsmPrimitive::getPrimitiveId));
//                for (OsmPrimitive primitive : toCleanUp) {
//                    removePrimitiveImpl(primitive);
//                }
//                firePrimitivesRemoved(toCleanUp, false);
//            }
//        });
    }

    /**
     * Removes all primitives from the dataset and resets the currently selected primitives
     * to the empty collection. Also notifies selection change listeners if necessary.
     * @throws IllegalStateException if the dataset is read-only
     */
    @Override
    public void clear() {
        //TODO: Why can't we clear a dataset that is locked?
        //TODO: Report listeners that are still active (should be none)
        checkModifiable();
        update(() -> {
            for (OsmPrimitive primitive : allPrimitives) {
                primitive.setDataset(null);
            }
            store.clear();
            allPrimitives.clear();
            //conflicts.get().clear();
        });
    }

    /**
     * Marks all "invisible" objects as deleted. These objects should be always marked as
     * deleted when downloaded from the server. They can be undeleted later if necessary.
     * @throws IllegalStateException if the dataset is read-only
     */
    public void deleteInvisible() {
        checkModifiable();
        for (OsmPrimitive primitive : allPrimitives) {
            if (!primitive.isVisible()) {
                primitive.setDeleted(true);
            }
        }
    }

    /**
     * Moves all primitives and datasources from DataSet "from" to this DataSet
     */
//    public void mergeFrom(DataSet from) {
//        mergeFrom(from, null);
//    }




    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Returns mappaint cache index for this DataSet.
     *
     * If the {@link OsmPrimitive#mappaintCacheIdx} is not equal to the DataSet mappaint
     * cache index, this means the cache for that primitive is out of date.
     * @return mappaint cache index
     * @since 13420
     */
    public short getMappaintCacheIndex() {
        return mappaintCacheIdx;
    }

    @Override
    public void lock() {
        if (!isReadOnly.compareAndSet(false, true)) {
            //Logging.warn("Trying to set readOnly flag on a readOnly dataset ", getName());
        }
    }

    @Override
    public void unlock() {
        if (!isReadOnly.compareAndSet(true, false)) {
            //Logging.warn("Trying to unset readOnly flag on a non-readOnly dataset ", getName());
        }
    }

    @Override
    public boolean isLocked() {
        return isReadOnly.get();
    }

    /**
     * Checks the dataset is modifiable (not read-only).
     * @throws IllegalStateException if the dataset is read-only
     */
    private void checkModifiable() {
        if (isLocked()) {
            throw new IllegalStateException("DataSet is read-only");
        }
    }

    /**
     * Returns an optional remark about this data set (used by Overpass API).
     * @return a remark about this data set, or {@code null}
     * @since 14219
     */
    public String getRemark() {
        return remark;
    }

    /**
     * Sets an optional remark about this data set (used by Overpass API).
     * @param remark a remark about this data set, or {@code null}
     * @since 14219
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * Determines if this Dataset contains no primitives.
     * @return true if this Dataset contains no primitives
     * @since 14835
     */
    public boolean isEmpty() {
        return allPrimitives.isEmpty();
    }
}
