// License: GPL. For details, see LICENSE file.
package com.hll.dataconvertservice.gts4vect.osm;


import com.hll.dataconvertservice.gts4vect.Compression;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.InvalidPathException;

/**
 * Exports data to an .osm file.
 * @since 1949
 */
public class OsmExporter {

    /**
     * Constructs a new {@code OsmExporter}.
     */
    public OsmExporter() {
    }


    /**
     * Exports OSM data to the given file.
     * @param file Output file
     * @param isAutosave if {@code true}, the potential backup file created if the output file already exists will be deleted
     *                   after a successful export and post-save events won't be fired
     * @throws IOException in case of IO errors
     * @throws InvalidPathException when file name cannot be converted into a Path
     * @throws IllegalArgumentException if {@code layer} is not an instance of {@code OsmDataLayer}
     */
    public void exportData(File file, DataSet dataSet, boolean isAutosave) throws IOException {
        doSave(file,dataSet);
    }

    protected static OutputStream getOutputStream(File file) throws IOException {
        return Compression.getCompressedFileOutputStream(file);
    }

    public static void doSave(File file, DataSet dataSet) throws IOException {
        // create outputstream and wrap it with gzip, xz or bzip, if necessary
        try (
            OutputStream out = getOutputStream(file);
            Writer writer = new OutputStreamWriter(out, StandardCharsets.UTF_8);
            OsmWriter w = OsmWriterFactory.createOsmWriter(new PrintWriter(writer), false, dataSet.getVersion())
        ) {
            dataSet.getReadLock().lock();
            try {
                w.write(dataSet);
            } finally {
                dataSet.getReadLock().unlock();
            }
        }
    }
}
