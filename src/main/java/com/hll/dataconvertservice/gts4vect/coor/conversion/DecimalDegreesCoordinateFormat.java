// License: GPL. For details, see LICENSE file.
package com.hll.dataconvertservice.gts4vect.coor.conversion;


import com.hll.dataconvertservice.gts4vect.coor.ILatLon;

import static com.hll.dataconvertservice.gts4vect.I18n.tr;


/**
 * Coordinate format that converts coordinates to simple floating point decimal format.
 * @since 12735
 */
public class DecimalDegreesCoordinateFormat extends AbstractCoordinateFormat {

    /**
     * The unique instance.
     */
    public static final DecimalDegreesCoordinateFormat INSTANCE = new DecimalDegreesCoordinateFormat();

    protected DecimalDegreesCoordinateFormat() {
        super("DECIMAL_DEGREES", tr("Decimal Degrees"));
    }

    @Override
    public String latToString(ILatLon ll) {
        return cDdFormatter.format(ll.lat());
    }

    @Override
    public String lonToString(ILatLon ll) {
        return cDdFormatter.format(ll.lon());
    }
}
