// License: GPL. For details, see LICENSE file.
package com.hll.dataconvertservice.gts4vect.coor;


import com.hll.dataconvertservice.gts4vect.osm.BBox;

/**
 * The minimum necessary interface to use {@link}.
 * <AUTHOR>
 * @since 17459
 */
@FunctionalInterface
public interface IQuadBucketType {
    /**
     * Fetches the bounding box of the primitive.
     * @return Bounding box of the object
     */
    BBox getBBox();
}
