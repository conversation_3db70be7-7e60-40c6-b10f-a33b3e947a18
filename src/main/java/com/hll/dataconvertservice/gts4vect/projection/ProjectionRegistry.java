// License: GPL. For details, see LICENSE file.
package com.hll.dataconvertservice.gts4vect.projection;

import com.hll.dataconvertservice.gts4vect.coor.Bounds;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;


/**
 * Registry for a single, global projection instance.
 * @since 14120
 */
public final class ProjectionRegistry {

    /**
     * The projection method used.
     * Use {@link #getProjection()} and {@link #setProjection(Projection)} for access.
     * Use {@link #setProjection(Projection)} in order to trigger a projection change event.
     */
    private static volatile Projection proj;

    private static ProjectionBoundsProvider boundsProvider;

    /*
     * Keep WeakReferences to the listeners. This relieves clients from the burden of
     * explicitly removing the listeners and allows us to transparently register every
     * created dataset as projection change listener.
     */
    private static final List<WeakReference<ProjectionChangeListener>> listeners = new CopyOnWriteArrayList<>();

    private ProjectionRegistry() {
        // hide constructor
    }

    /**
     * Replies the current projection.
     *
     * @return the currently active projection
     */
    public static Projection getProjection() {
        return proj;
    }

    /**
     * Sets the current projection
     *
     * @param p the projection
     */
    public static void setProjection(Projection p) {
        Projection oldValue = proj;
        Bounds b = boundsProvider != null ? boundsProvider.getRealBounds() : null;
        proj = p;
        fireProjectionChanged(oldValue, proj, b);
    }

    private static void fireProjectionChanged(Projection oldValue, Projection newValue, Bounds oldBounds) {
        if ((newValue == null ^ oldValue == null)
                || (newValue != null && oldValue != null && !Objects.equals(newValue.toCode(), oldValue.toCode()))) {
            listeners.removeIf(x -> x.get() == null);
            listeners.stream().map(WeakReference::get).filter(Objects::nonNull).forEach(x -> x.projectionChanged(oldValue, newValue));
            if (newValue != null && oldBounds != null && boundsProvider != null) {
                boundsProvider.restoreOldBounds(oldBounds);
            }
            /* TODO - remove layers with fixed projection */
        }
    }

}
