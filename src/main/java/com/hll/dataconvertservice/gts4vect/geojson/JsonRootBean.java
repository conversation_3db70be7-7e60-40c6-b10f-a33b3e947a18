/**
  * Copyright 2022 bejson.com 
  */
package com.hll.dataconvertservice.gts4vect.geojson;
import java.util.List;

/**
 * Auto-generated: 2022-03-11 15:44:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class JsonRootBean {

    private String type;
    private List<Features> features;
    public void setType(String type) {
         this.type = type;
     }
     public String getType() {
         return type;
     }

    public void setFeatures(List<Features> features) {
         this.features = features;
     }
     public List<Features> getFeatures() {
         return features;
     }

}