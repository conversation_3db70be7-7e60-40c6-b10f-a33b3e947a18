/**
  * Copyright 2022 bejson.com 
  */
package com.hll.dataconvertservice.gts4vect.geojson;
import java.util.List;

/**
 * Auto-generated: 2022-03-11 15:44:46
 *
 * <AUTHOR> (<EMAIL>)
 * @website http://www.bejson.com/java2pojo/
 */
public class Geometry {

    private String type;

    public List<Double> getCoordinates() {
        return coordinates;
    }

    public void setCoordinates(List<Double> coordinates) {
        this.coordinates = coordinates;
    }

    private List<Double> coordinates;


//    public List<List<Double>> getMultiCoords() {
//        return multiCoords;
//    }
//
//    public void setMultiCoords(List<List<Double>> multiCoords) {
//        this.multiCoords = multiCoords;
//    }
//
//    private List<List<Double>> multiCoords;
//    private List<Double> coordinates;
//    public void setType(String type) {
//         this.type = type;
//     }
//     public String getType() {
//         return type;
//     }
//
//    public void setCoordinates(List<Double> coordinates) {
//         this.coordinates = coordinates;
//     }
//     public List<Double> getCoordinates() {
//         return coordinates;
//     }

}