// License: GPL. For details, see LICENSE file.
package com.hll.dataconvertservice.gts4vect;



import com.hll.dataconvertservice.gts4vect.coor.LatLon;
import com.hll.dataconvertservice.gts4vect.osm.*;

import javax.json.*;
import javax.json.stream.JsonParser;
import javax.json.stream.JsonParser.Event;
import javax.json.stream.JsonParsingException;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Reader that reads GeoJSON files. See <a href="https://tools.ietf.org/html/rfc7946">RFC7946</a> for more information.
 * @since 15424
 */
public class GeoJSONReader {

    private static final String CRS = "crs";
    private static final String NAME = "name";
    private static final String LINK = "link";
    private static final String COORDINATES = "coordinates";
    private static final String FEATURES = "features";
    private static final String PROPERTIES = "properties";
    private static final String GEOMETRY = "geometry";
    private static final String TYPE = "type";
    /** The record separator is 0x1E per RFC 7464 */
    private static final byte RECORD_SEPARATOR_BYTE = 0x1E;
    protected final Map<PrimitiveId, OsmPrimitive> externalIdMap = new HashMap<>();

    /**
     * Data structure for the remaining way objects
     */
    protected final Map<Long, Collection<Long>> ways = new HashMap<>();

    /**
     * Data structure for relation objects
     */
    protected final Map<Long, Collection<RelationMemberData>> relations = new HashMap<>();

    protected DataSet ds = new DataSet();
    public DataSet getDataSet() {
        return ds;
    }

    GeoJSONReader() {}

    private void parse(final JsonParser parser) {
        while (parser.hasNext()) {
            Event event = parser.next();
            if (event == Event.START_OBJECT) {
                parseRoot(parser.getObject());
            }
        }
        parser.close();
    }

    private void parseRoot(final JsonObject object)  {
        parseCrs(object.getJsonObject(CRS));
        switch (object.getJsonString(TYPE).getString()) {
            case "FeatureCollection":
                JsonValue.ValueType valueType = object.get(FEATURES).getValueType();
                parseFeatureCollection(object.getJsonArray(FEATURES));
                break;
            case "Feature":
                parseFeature(object);
                break;
            case "GeometryCollection":
                parseGeometryCollection(null, object);
                break;
            default:
                parseGeometry(null, object);
        }
    }

    /**
     * Parse CRS as per https://geojson.org/geojson-spec.html#coordinate-reference-system-objects.
     * CRS are obsolete in RFC7946 but still allowed for interoperability with older applications.
     * Only named CRS are supported.
     *
     * @param crs CRS JSON object
     */
    private void parseCrs(final JsonObject crs) {
        if (crs != null) {
            // Inspired by https://github.com/JOSM/geojson/commit/f13ceed4645244612a63581c96e20da802779c56
            JsonObject properties = crs.getJsonObject("properties");
            if (properties != null) {
                switch (crs.getString(TYPE)) {
                    case NAME:
                        String crsName = properties.getString(NAME);
                        if ("urn:ogc:def:crs:OGC:1.3:CRS84".equals(crsName)) {
                            // https://osgeo-org.atlassian.net/browse/GEOT-1710
                            crsName = "EPSG:4326";
                        } else if (crsName.startsWith("urn:ogc:def:crs:EPSG:")) {
                            crsName = crsName.replace("urn:ogc:def:crs:", "");
                        }
                        break;
                    case LINK: // Not supported (security risk)
                    default:
                }
            }
        }
    }

    private void parseFeatureCollection(final JsonArray features) {
        for (JsonValue feature : features) {
            if (feature instanceof JsonObject) {
                parseFeature((JsonObject) feature);
            }
        }
    }

    private void parseFeature(final JsonObject feature) {
        JsonValue geometry = feature.get(GEOMETRY);
        if (geometry != null && geometry.getValueType() == JsonValue.ValueType.OBJECT) {
            parseGeometry(feature, geometry.asJsonObject());
        } else {
            JsonValue properties = feature.get(PROPERTIES);
            if (properties != null && properties.getValueType() == JsonValue.ValueType.OBJECT) {
                parseNonGeometryFeature(feature, properties.asJsonObject());
            } else {
//                Logging.warn(tr("Relation/non-geometry feature without properties found: {0}", feature));
            }
        }
    }

    private void parseNonGeometryFeature(final JsonObject feature, final JsonObject properties) {
        // get relation type
        JsonValue type = properties.get(TYPE);
        if (type == null || properties.getValueType() == JsonValue.ValueType.STRING) {
//            Logging.warn(tr("Relation/non-geometry feature without type found: {0}", feature));
            return;
        }

        // create misc. non-geometry feature
        final Relation relation = new Relation();
        fillTagsFromFeature(feature, relation);
        relation.put(TYPE, type.toString());
        getDataSet().addPrimitive(relation);
    }

    private void parseGeometryCollection(final JsonObject feature, final JsonObject geometry) {
        for (JsonValue jsonValue : geometry.getJsonArray("geometries")) {
            parseGeometry(feature, jsonValue.asJsonObject());
        }
    }

    private void parseGeometry(final JsonObject feature, final JsonObject geometry) {
        if (geometry == null) {
            parseNullGeometry(feature);
            return;
        }

        switch (geometry.getString(TYPE)) {
            case "Point":
                parsePoint(feature, geometry.getJsonArray(COORDINATES));
                break;
            case "MultiPoint":
                parseMultiPoint(feature, geometry);
                break;
            case "LineString":
                parseLineString(feature, geometry.getJsonArray(COORDINATES));
                break;
            case "MultiLineString":
                parseMultiLineString(feature, geometry);
                break;
            case "Polygon":
                parsePolygon(feature, geometry.getJsonArray(COORDINATES));
                break;
            case "MultiPolygon":
                parseMultiPolygon(feature, geometry);
                break;
            case "GeometryCollection":
                parseGeometryCollection(feature, geometry);
                break;
            default:
                parseUnknown(geometry);
        }
    }

    private LatLon getLatLon(final JsonArray coordinates) {
        return  new LatLon(parseCoordinate(coordinates.get(1)),
                parseCoordinate(coordinates.get(0)));
    }

    private static double parseCoordinate(JsonValue coordinate) {
        if (coordinate instanceof JsonString) {
            return Double.parseDouble(((JsonString) coordinate).getString());
        } else if (coordinate instanceof JsonNumber) {
            return ((JsonNumber) coordinate).doubleValue();
        } else {
            throw new IllegalArgumentException(Objects.toString(coordinate));
        }
    }

    private void parsePoint(final JsonObject feature, final JsonArray coordinates) {
        fillTagsFromFeature(feature, createNode(getLatLon(coordinates)));
    }

    private void parseMultiPoint(final JsonObject feature, final JsonObject geometry) {
        for (JsonValue coordinate : geometry.getJsonArray(COORDINATES)) {
            parsePoint(feature, coordinate.asJsonArray());
        }
    }

    private void parseLineString(final JsonObject feature, final JsonArray coordinates) {
        if (!coordinates.isEmpty()) {
            createWay(coordinates, false)
                .ifPresent(way -> fillTagsFromFeature(feature, way));
        }
    }

    private void parseMultiLineString(final JsonObject feature, final JsonObject geometry) {
        for (JsonValue coordinate : geometry.getJsonArray(COORDINATES)) {
            parseLineString(feature, coordinate.asJsonArray());
        }
    }

    private void parsePolygon(final JsonObject feature, final JsonArray coordinates) {
        final int size = coordinates.size();
        if (size == 1) {
            createWay(coordinates.getJsonArray(0), true)
                .ifPresent(way -> fillTagsFromFeature(feature, way));
        } else if (size > 1) {
            // create multipolygon
            final Relation multipolygon = new Relation();
            createWay(coordinates.getJsonArray(0), true)
                .ifPresent(way -> multipolygon.addMember(new RelationMember("outer", way)));

            for (JsonValue interiorRing : coordinates.subList(1, size)) {
                createWay(interiorRing.asJsonArray(), true)
                    .ifPresent(way -> multipolygon.addMember(new RelationMember("inner", way)));
            }

            fillTagsFromFeature(feature, multipolygon);
            multipolygon.put(TYPE, "multipolygon");
        }
    }

    private void parseMultiPolygon(final JsonObject feature, final JsonObject geometry) {
        for (JsonValue coordinate : geometry.getJsonArray(COORDINATES)) {
            parsePolygon(feature, coordinate.asJsonArray());
        }
    }

    private Node createNode(final LatLon latlon) {
        double lon =0.0,lat=0.0;

        if (latlon.getX() > 1000){
            lon = latlon.getX()/100;
        }
        if (latlon.getY() > 100){
            lat = latlon.getY()/100;
        }

        LatLon newLatlon = new LatLon(lat,lon);
        final List<Node> existingNodes = getDataSet().searchNodes(new BBox(newLatlon, newLatlon));
        if (!existingNodes.isEmpty()) {
            // reuse existing node, avoid multiple nodes on top of each other
            return existingNodes.get(0);
        }
        final Node node = new Node(newLatlon);
        getDataSet().addPrimitive(node);
        return node;
    }

    private Optional<Way> createWay(final JsonArray coordinates, final boolean autoClose) {
        if (coordinates.isEmpty()) {
            return Optional.empty();
        }

        final List<LatLon> latlons = coordinates.stream()
                .map(coordinate -> getLatLon(coordinate.asJsonArray()))
                .collect(Collectors.toList());

        final int size = latlons.size();
        final boolean doAutoclose;
        if (size > 1) {
            if (latlons.get(0).equals(latlons.get(size - 1))) {
                doAutoclose = false; // already closed
            } else {
                doAutoclose = autoClose;
            }
        } else {
            doAutoclose = false;
        }

        final Way way = new Way();
        getDataSet().addPrimitive(way);
        final List<Node> rawNodes = latlons.stream().map(this::createNode).collect(Collectors.toList());
        if (doAutoclose) {
            rawNodes.add(rawNodes.get(0));
        }
        // see #19833: remove duplicated references to the same node
        final List<Node> wayNodes = new ArrayList<>(rawNodes.size());
        Node last = null;
        for (Node curr : rawNodes) {
            if (last != curr) {
                wayNodes.add(curr);
            }
            last = curr;
        }
        way.setNodes(wayNodes);

        return Optional.of(way);
    }

    /**
     * Merge existing tags in primitive (if any) with the values given in the GeoJSON feature.
     * @param feature the GeoJSON feature
     * @param primitive the OSM primitive
     */
    private static void fillTagsFromFeature(final JsonObject feature, final OsmPrimitive primitive) {
        if (feature != null) {
            TagCollection featureTags = getTags(feature);
            primitive.setKeys(new TagMap(featureTags));
        }
    }

    private static void parseUnknown(final JsonObject object) {
        //Logging.warn(tr("Unknown json object found {0}", object));
    }

    private static void parseNullGeometry(JsonObject feature) {
        //Logging.warn(tr("Geometry of feature {0} is null", feature));
    }

    private static TagCollection getTags(final JsonObject feature) {
        final TagCollection tags = new TagCollection();

        if (feature.containsKey(PROPERTIES) && !feature.isNull(PROPERTIES)) {
            JsonValue properties = feature.get(PROPERTIES);
            if (properties != null && properties.getValueType() == JsonValue.ValueType.OBJECT) {
                for (Map.Entry<String, JsonValue> stringJsonValueEntry : properties.asJsonObject().entrySet()) {
                    final JsonValue value = stringJsonValueEntry.getValue();

                    if (value instanceof JsonString) {
                        tags.add(new Tag(stringJsonValueEntry.getKey(), ((JsonString) value).getString()));
                    } else if (value instanceof JsonObject) {
//                        Logging.warn(
//                            "The GeoJSON contains an object with property '" + stringJsonValueEntry.getKey()
//                                + "' whose value has the unsupported type '" + value.getClass().getSimpleName()
//                                + "'. That key-value pair is ignored!"
//                        );
                    } else if (value.getValueType() != JsonValue.ValueType.NULL) {
                        tags.add(new Tag(stringJsonValueEntry.getKey(), value.toString()));
                    }
                }
            }
        }
        return tags;
    }

    /**
     * Check if the inputstream follows RFC 7464
     * @param source The source to check (should be at the beginning)
     * @return {@code true} if the initial character is {@link GeoJSONReader#RECORD_SEPARATOR_BYTE}.
     */
    private static boolean isLineDelimited(InputStream source) {
        source.mark(2);
        try {
            int start = source.read();
            if (RECORD_SEPARATOR_BYTE == start) {
                return true;
            }
            source.reset();
        } catch (IOException e) {
            //Logging.error(e);
        }
        return false;
    }

    protected DataSet doParseDataSet(InputStream source) {
        try (InputStream markSupported = source.markSupported() ? source : new BufferedInputStream(source)) {
            ds.setUploadPolicy(UploadPolicy.DISCOURAGED);
            if (isLineDelimited(markSupported)) {
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(markSupported, StandardCharsets.UTF_8))) {
                    String line;
                    String rs = new String(new byte[]{RECORD_SEPARATOR_BYTE}, StandardCharsets.US_ASCII);
                    while ((line = reader.readLine()) != null) {
                        line = Utils.strip(line, rs);
                        try (JsonParser parser = Json.createParser(new StringReader(line))) {
                            parse(parser);
                        }
                    }
                }
            } else {
                try (JsonParser parser = Json.createParser(markSupported)) {
                    parse(parser);
                }
            }
        } catch (IOException | IllegalArgumentException | JsonParsingException e) {
            //throw new IllegalDataException(e);
        }
        prepareDataSet();
        return getDataSet();
    }

    /**
     * Parse the given input source and return the dataset.
     *
     * @param source          the source input stream. Must not be null.
     * @return the dataset with the parsed data
     * @throws IllegalArgumentException if source is null
     */
    public static DataSet parseDataSet(InputStream source) {
        return new GeoJSONReader().doParseDataSet(source);
    }

    protected void processNodesAfterParsing() {
        for (OsmPrimitive primitive: externalIdMap.values()) {
            if (primitive instanceof Node) {
                this.ds.addPrimitive(primitive);
            }
        }
    }

    protected void processWaysAfterParsing() {
        for (Map.Entry<Long, Collection<Long>> entry : ways.entrySet()) {
            Long externalWayId = entry.getKey();
            Way w = (Way) externalIdMap.get(new SimplePrimitiveId(externalWayId, OsmPrimitiveType.WAY));
            List<Node> wayNodes = new ArrayList<>();
            for (long id : entry.getValue()) {
                Node n = (Node) externalIdMap.get(new SimplePrimitiveId(id, OsmPrimitiveType.NODE));
                if (n == null) {
                    if (id <= 0) {
//                        throw new IllegalDataException(
//                                tr("Way with external ID ''{0}'' includes missing node with external ID ''{1}''.",
//                                        Long.toString(externalWayId),
//                                        Long.toString(id)));
                        // create an incomplete node if necessary
                    }
                    n = (Node) ds.getPrimitiveById(id, OsmPrimitiveType.NODE);
                    if (n == null) {
                        n = new Node(id);
                        ds.addPrimitive(n);
                    }
                }
                if (n.isDeleted()) {
                    //Logging.info(tr("Deleted node {0} is part of way {1}", Long.toString(id), Long.toString(w.getId())));
                } else {
                    wayNodes.add(n);
                }
            }
            w.setNodes(wayNodes);
            if (w.hasIncompleteNodes()) {
//                Logging.info(tr("Way {0} with {1} nodes is incomplete because at least one node was missing in the loaded data.",
//                        Long.toString(externalWayId), w.getNodesCount()));
            }
            ds.addPrimitive(w);
        }
    }

    /**
     * Completes the parsed relations with its members.
     *
     * relation member refers to a local primitive which wasn't available in the data
     */
    protected void processRelationsAfterParsing() {

        // First add all relations to make sure that when relation reference other relation, the referenced will be already in dataset
        for (Long externalRelationId : relations.keySet()) {
            Relation relation = (Relation) externalIdMap.get(
                    new SimplePrimitiveId(externalRelationId, OsmPrimitiveType.RELATION)
            );
            ds.addPrimitive(relation);
        }

        for (Map.Entry<Long, Collection<RelationMemberData>> entry : relations.entrySet()) {
            Long externalRelationId = entry.getKey();
            Relation relation = (Relation) externalIdMap.get(
                    new SimplePrimitiveId(externalRelationId, OsmPrimitiveType.RELATION)
            );
            List<RelationMember> relationMembers = new ArrayList<>();
            for (RelationMemberData rm : entry.getValue()) {
                // lookup the member from the map of already created primitives
                OsmPrimitive primitive = externalIdMap.get(new SimplePrimitiveId(rm.getMemberId(), rm.getMemberType()));

                if (primitive == null) {
                    if (rm.getMemberId() <= 0){
                        // relation member refers to a primitive with a negative id which was not
                        // found in the data. This is always a data integrity problem and we abort
                        // with an exception
                        //
//                        throw new IllegalDataException(
//                                tr("Relation with external id ''{0}'' refers to a missing primitive with external id ''{1}''.",
//                                        Long.toString(externalRelationId),
//                                        Long.toString(rm.getMemberId())));

                    // member refers to OSM primitive which was not present in the parsed data
                    // -> create a new incomplete primitive and add it to the dataset
                    //
                    }
                    primitive = ds.getPrimitiveById(rm.getMemberId(), rm.getMemberType());
                    if (primitive == null) {
                        switch (rm.getMemberType()) {
                            case NODE:
                                primitive = new Node(rm.getMemberId()); break;
                            case WAY:
                                primitive = new Way(rm.getMemberId()); break;
                            case RELATION:
                                primitive = new Relation(rm.getMemberId()); break;
                            default: throw new AssertionError(); // can't happen
                        }

                        ds.addPrimitive(primitive);
                        externalIdMap.put(new SimplePrimitiveId(rm.getMemberId(), rm.getMemberType()), primitive);
                    }
                }
                if (primitive.isDeleted()) {
//                    Logging.info(tr("Deleted member {0} is used by relation {1}",
//                            Long.toString(primitive.getId()), Long.toString(relation.getId())));
                } else {
                    relationMembers.add(new RelationMember(rm.getRole(), primitive));
                }
            }
            relation.setMembers(relationMembers);
        }
    }
    protected final void prepareDataSet() {
        ds.beginUpdate();
        try {
            processNodesAfterParsing();
            processWaysAfterParsing();
            processRelationsAfterParsing();
        } finally {
            ds.endUpdate();
        }
    }

}
