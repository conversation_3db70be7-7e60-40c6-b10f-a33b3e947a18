package com.hll.dataconvertservice.gts4vect;

import java.nio.charset.Charset;

public class ShpCharset {
    public static final Charset GBK = Charset.forName("GBK");
    public static final Charset UTF_8 = Charset.forName("UTF-8");
    public static final Charset GB2312 = Charset.forName("GB2312");
    public static final Charset ISO_8859_1 = Charset.forName("ISO_8859_1");
    public static final Charset ASCII = Charset.forName("ASCII");
}
