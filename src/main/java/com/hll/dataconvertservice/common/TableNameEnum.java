package com.hll.dataconvertservice.common;

public enum TableNameEnum {
    // link_e -> link
    LINK_E("link_e", "link"),
    // node_e -> node
    NODE_E("node_e", "node"),
    // relation_e -> relation
    RELATION_E("relation_e", "relation"),
    // rule_e -> rule
    RULE_E("rule_e", "rule");

    private final String sourceTableName;
    private final String targetTableName;

    TableNameEnum(String sourceTableName, String targetTableName) {
        this.sourceTableName = sourceTableName;
        this.targetTableName = targetTableName;
    }
    public String getSourceTableName() {
        return sourceTableName;
    }

    public String getTargetTableName() {
        return targetTableName;
    }
    public static String getTargetTableName(String sourceTableName) {
        for (TableNameEnum tableNameEnum : TableNameEnum.values()) {
            if (tableNameEnum.getSourceTableName().equals(sourceTableName)) {
                return tableNameEnum.getTargetTableName();
            }
        }
        return null;
    }
}
