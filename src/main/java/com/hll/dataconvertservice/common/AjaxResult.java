package com.hll.dataconvertservice.common;

import java.util.HashMap;

public class AjaxResult
        extends HashMap<String, Object> {
    private static final long serialVersionUID = 1L;
    public static final String CODE_TAG = "code";
    public static final String MSG_TAG = "msg";
    public static final String DATA_TAG = "data";

    public static AjaxResult success(Object data) {
        /* 76*/
        return AjaxResult.success("操作成功", data);
    }

    public static AjaxResult success() {
        /* 67*/
        return AjaxResult.success("操作成功");
    }

    public static AjaxResult success(String msg) {
        /* 86*/
        return AjaxResult.success(msg, null);
    }

    public static AjaxResult success(String msg, Object data) {
        return new AjaxResult(200, msg, data);
    }

    public AjaxResult() {
    }

    public AjaxResult(int code, String msg, Object data) {
        /* 54*/
        super.put(CODE_TAG, code);
        /* 55*/
        super.put(MSG_TAG, msg);
        /* 56*/
        if (data != null) {
            /* 57*/
            super.put(DATA_TAG, data);
        }
    }

    public AjaxResult(int code, String msg) {
        /* 42*/
        super.put(CODE_TAG, code);
        /* 43*/
        super.put(MSG_TAG, msg);
    }

    @Override
    public AjaxResult put(String key, Object value) {
        /*150*/
        super.put(key, value);
        /*151*/
        return this;
    }

    public static AjaxResult error(String msg, Object data) {
        return new AjaxResult(500, msg, data);
    }

    public static AjaxResult error() {
        /*106*/
        return AjaxResult.error("操作失败");
    }

    public static AjaxResult error(int code, String msg) {
        return new AjaxResult(code, msg, null);
    }

    public static AjaxResult error(String msg) {
        /*116*/
        return AjaxResult.error(msg, null);
    }
}