package com.hll.dataconvertservice.service;

import com.hll.dataconvertservice.config.S3Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.S3Exception;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@Service
public class S3Service {
    
    @Autowired
    private S3Client s3Client;
    
    @Autowired
    private S3Config s3Config;
    
    /**
     * Upload all files from a directory to S3 bucket
     * 
     * @param localDirectoryPath Local directory path containing files to upload
     * @param s3KeyPrefix S3 key prefix (folder path in S3)
     * @return List of successfully uploaded file names
     */
    public List<String> uploadDirectoryToS3(String localDirectoryPath, String s3KeyPrefix) {
        List<String> uploadedFiles = new ArrayList<>();
        
        try {
            Path directoryPath = Paths.get(localDirectoryPath);
            
            if (!Files.exists(directoryPath) || !Files.isDirectory(directoryPath)) {
                log.warn("Directory does not exist or is not a directory: {}", localDirectoryPath);
                return uploadedFiles;
            }
            
            // Get all files in the directory (non-recursive)
            try (Stream<Path> files = Files.list(directoryPath)) {
                files.filter(Files::isRegularFile)
                     .forEach(filePath -> {
                         try {
                             String fileName = filePath.getFileName().toString();
                             String s3Key = s3KeyPrefix.endsWith("/") ? 
                                 s3KeyPrefix + fileName : 
                                 s3KeyPrefix + "/" + fileName;
                             
                             if (uploadFileToS3(filePath.toString(), s3Key)) {
                                 uploadedFiles.add(fileName);
                                 log.info("Successfully uploaded file: {} to S3 key: {}", fileName, s3Key);
                             } else {
                                 log.error("Failed to upload file: {}", fileName);
                             }
                         } catch (Exception e) {
                             log.error("Error uploading file: {}, error: {}", filePath.getFileName(), e.getMessage());
                         }
                     });
            }
            
        } catch (IOException e) {
            log.error("Error reading directory: {}, error: {}", localDirectoryPath, e.getMessage());
        }
        
        return uploadedFiles;
    }
    
    /**
     * Upload a single file to S3
     * 
     * @param localFilePath Local file path
     * @param s3Key S3 key (object name in bucket)
     * @return true if upload successful, false otherwise
     */
    public boolean uploadFileToS3(String localFilePath, String s3Key) {
        try {
            File file = new File(localFilePath);
            
            if (!file.exists() || !file.isFile()) {
                log.warn("File does not exist or is not a file: {}", localFilePath);
                return false;
            }
            localFilePath = localFilePath.startsWith("/")? localFilePath.substring(1) : localFilePath;
            String localFileName = localFilePath.substring(localFilePath.lastIndexOf("/") + 1);
            String handleS3Key = s3Key.endsWith("/") ? s3Key + localFileName : s3Key + "/" + localFileName;
            log.info("upload file is {}, handleS3Key is {}", localFileName, handleS3Key);

            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(s3Config.getBucketName())
                    .key(handleS3Key)
                    .contentType(getContentType(localFilePath))
                    .build();
            
            s3Client.putObject(putObjectRequest, RequestBody.fromFile(file));
            
            log.debug("File uploaded successfully: {} -> s3://{}/{}", 
                localFilePath, s3Config.getBucketName(), s3Key);
            return true;
            
        } catch (S3Exception e) {
            log.error("S3 error uploading file: {}, error: {}", localFilePath, e.awsErrorDetails().errorMessage());
            return false;
        } catch (Exception e) {
            log.error("Error uploading file: {}, error: {}", localFilePath, e.getMessage());
            return false;
        }
    }
    
    /**
     * Determine content type based on file extension
     */
    private String getContentType(String filePath) {
        String fileName = Paths.get(filePath).getFileName().toString().toLowerCase();
        
        if (fileName.endsWith(".geojson") || fileName.endsWith(".json")) {
            return "application/geo+json";
        } else if (fileName.endsWith(".txt")) {
            return "text/plain";
        } else if (fileName.endsWith(".csv")) {
            return "text/csv";
        } else if (fileName.endsWith(".xml")) {
            return "application/xml";
        }
        
        return "application/octet-stream"; // Default binary content type
    }
}
