package com.hll.dataconvertservice.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.apache.commons.compress.utils.IOUtils;

import java.io.*;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

/**
 * Comprehensive file compression and decompression utility class.
 * Supports multiple archive formats: TAR.GZ, TAR, ZIP
 * 
 * <AUTHOR>
 */
@Slf4j
public class ArchiveUtils {

    /**
     * Supported archive formats
     */
    public enum ArchiveFormat {
        TAR_GZ(".tar.gz"),
        TAR(".tar"),
        ZIP(".zip");

        private final String extension;

        ArchiveFormat(String extension) {
            this.extension = extension;
        }

        public String getExtension() {
            return extension;
        }

        /**
         * Determine format by file extension
         */
        public static ArchiveFormat fromFileName(String fileName) {
            if (fileName.toLowerCase().endsWith(".tar.gz") || fileName.toLowerCase().endsWith(".tgz")) {
                return TAR_GZ;
            } else if (fileName.toLowerCase().endsWith(".tar")) {
                return TAR;
            } else if (fileName.toLowerCase().endsWith(".zip")) {
                return ZIP;
            }
            throw new IllegalArgumentException("Unsupported archive format for file: " + fileName);
        }
    }

    // ==================== COMPRESSION METHODS ====================

    /**
     * Compress a list of files to TAR.GZ format (enhanced version of original method)
     * 
     * @param files List of files to compress
     * @param tarGzFileName Output TAR.GZ file path
     * @throws IOException if compression fails
     */
    public static void compressFilesToTarGz(List<File> files, String tarGzFileName) throws IOException {
        compressFiles(files, tarGzFileName, ArchiveFormat.TAR_GZ);
    }

    /**
     * Compress a list of files to the specified archive format
     * 
     * @param files List of files to compress
     * @param archiveFileName Output archive file path
     * @param format Archive format
     * @throws IOException if compression fails
     */
    public static void compressFiles(List<File> files, String archiveFileName, ArchiveFormat format) throws IOException {
        if (files == null || files.isEmpty()) {
            throw new IllegalArgumentException("文件列表不能为空");
        }

        File archiveFile = new File(archiveFileName);
        ensureParentDirectoryExists(archiveFile);

        switch (format) {
            case TAR_GZ:
                compressFilesToTarGzInternal(files, archiveFile);
                break;
            case TAR:
                compressFilesToTarInternal(files, archiveFile);
                break;
            case ZIP:
                compressFilesToZipInternal(files, archiveFile);
                break;
            default:
                throw new IllegalArgumentException("Unsupported archive format: " + format);
        }

        log.info("成功压缩 {} 个文件到: {} (格式: {})", files.size(), archiveFileName, format);
    }

    /**
     * Compress a directory to the specified archive format
     * 
     * @param directoryPath Directory to compress
     * @param archiveFileName Output archive file path
     * @param format Archive format
     * @throws IOException if compression fails
     */
    public static void compressDirectory(String directoryPath, String archiveFileName, ArchiveFormat format) throws IOException {
        Path sourceDir = Paths.get(directoryPath);
        if (!Files.exists(sourceDir) || !Files.isDirectory(sourceDir)) {
            throw new IllegalArgumentException("目录不存在或不是有效目录: " + directoryPath);
        }

        File archiveFile = new File(archiveFileName);
        ensureParentDirectoryExists(archiveFile);

        switch (format) {
            case TAR_GZ:
                compressDirectoryToTarGz(sourceDir, archiveFile);
                break;
            case TAR:
                compressDirectoryToTar(sourceDir, archiveFile);
                break;
            case ZIP:
                compressDirectoryToZip(sourceDir, archiveFile);
                break;
            default:
                throw new IllegalArgumentException("Unsupported archive format: " + format);
        }

        log.info("成功压缩目录 {} 到: {} (格式: {})", directoryPath, archiveFileName, format);
    }

    // ==================== DECOMPRESSION METHODS ====================

    /**
     * Extract an archive to the specified directory
     * 
     * @param archiveFileName Archive file path
     * @param extractToDirectory Target extraction directory
     * @throws IOException if extraction fails
     */
    public static void extractArchive(String archiveFileName, String extractToDirectory) throws IOException {
        ArchiveFormat format = ArchiveFormat.fromFileName(archiveFileName);
        extractArchive(archiveFileName, extractToDirectory, format);
    }

    /**
     * Extract an archive to the specified directory with explicit format
     * 
     * @param archiveFileName Archive file path
     * @param extractToDirectory Target extraction directory
     * @param format Archive format
     * @throws IOException if extraction fails
     */
    public static void extractArchive(String archiveFileName, String extractToDirectory, ArchiveFormat format) throws IOException {
        File archiveFile = new File(archiveFileName);
        if (!archiveFile.exists() || !archiveFile.isFile()) {
            throw new IllegalArgumentException("压缩文件不存在: " + archiveFileName);
        }

        Path extractDir = Paths.get(extractToDirectory);
        Files.createDirectories(extractDir);

        switch (format) {
            case TAR_GZ:
                extractTarGz(archiveFile, extractDir);
                break;
            case TAR:
                extractTar(archiveFile, extractDir);
                break;
            case ZIP:
                extractZip(archiveFile, extractDir);
                break;
            default:
                throw new IllegalArgumentException("Unsupported archive format: " + format);
        }

        log.info("成功解压 {} 到目录: {} (格式: {})", archiveFileName, extractToDirectory, format);
    }

    // ==================== INTERNAL COMPRESSION METHODS ====================

    private static void compressFilesToTarGzInternal(List<File> files, File tarGzFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(tarGzFile);
             GzipCompressorOutputStream gzos = new GzipCompressorOutputStream(fos);
             TarArchiveOutputStream taos = new TarArchiveOutputStream(gzos)) {

            taos.setLongFileMode(TarArchiveOutputStream.LONGFILE_GNU);
            addFilesToTarArchive(taos, files);
            taos.finish();
        }
    }

    private static void compressFilesToTarInternal(List<File> files, File tarFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(tarFile);
             TarArchiveOutputStream taos = new TarArchiveOutputStream(fos)) {

            taos.setLongFileMode(TarArchiveOutputStream.LONGFILE_GNU);
            addFilesToTarArchive(taos, files);
            taos.finish();
        }
    }

    private static void compressFilesToZipInternal(List<File> files, File zipFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            for (File file : files) {
                if (!file.exists()) {
                    log.warn("文件不存在，跳过: {}", file.getAbsolutePath());
                    continue;
                }

                if (!file.isFile()) {
                    log.warn("不是普通文件，跳过: {}", file.getAbsolutePath());
                    continue;
                }

                ZipEntry entry = new ZipEntry(file.getName());
                zos.putNextEntry(entry);

                try (FileInputStream fis = new FileInputStream(file)) {
                    IOUtils.copy(fis, zos);
                }

                zos.closeEntry();
                log.debug("已添加文件到ZIP压缩包: {}", file.getName());
            }
        }
    }

    private static void addFilesToTarArchive(TarArchiveOutputStream taos, List<File> files) throws IOException {
        for (File file : files) {
            if (!file.exists()) {
                log.warn("文件不存在，跳过: {}", file.getAbsolutePath());
                continue;
            }

            if (!file.isFile()) {
                log.warn("不是普通文件，跳过: {}", file.getAbsolutePath());
                continue;
            }

            TarArchiveEntry entry = new TarArchiveEntry(file, file.getName());
            taos.putArchiveEntry(entry);

            try (FileInputStream fis = new FileInputStream(file)) {
                IOUtils.copy(fis, taos);
            }

            taos.closeArchiveEntry();
            log.debug("已添加文件到TAR压缩包: {}", file.getName());
        }
    }

    // ==================== DIRECTORY COMPRESSION METHODS ====================

    private static void compressDirectoryToTarGz(Path sourceDir, File tarGzFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(tarGzFile);
             GzipCompressorOutputStream gzos = new GzipCompressorOutputStream(fos);
             TarArchiveOutputStream taos = new TarArchiveOutputStream(gzos)) {

            taos.setLongFileMode(TarArchiveOutputStream.LONGFILE_GNU);
            addDirectoryToTarArchive(taos, sourceDir, sourceDir);
            taos.finish();
        }
    }

    private static void compressDirectoryToTar(Path sourceDir, File tarFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(tarFile);
             TarArchiveOutputStream taos = new TarArchiveOutputStream(fos)) {

            taos.setLongFileMode(TarArchiveOutputStream.LONGFILE_GNU);
            addDirectoryToTarArchive(taos, sourceDir, sourceDir);
            taos.finish();
        }
    }

    private static void compressDirectoryToZip(Path sourceDir, File zipFile) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(zipFile);
             ZipOutputStream zos = new ZipOutputStream(fos)) {

            Files.walkFileTree(sourceDir, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    String entryName = sourceDir.relativize(file).toString().replace('\\', '/');
                    ZipEntry entry = new ZipEntry(entryName);
                    zos.putNextEntry(entry);

                    try (InputStream is = Files.newInputStream(file)) {
                        IOUtils.copy(is, zos);
                    }

                    zos.closeEntry();
                    log.debug("已添加文件到ZIP压缩包: {}", entryName);
                    return FileVisitResult.CONTINUE;
                }
            });
        }
    }

    private static void addDirectoryToTarArchive(TarArchiveOutputStream taos, Path sourceDir, Path baseDir) throws IOException {
        Files.walkFileTree(sourceDir, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                String entryName = baseDir.relativize(file).toString().replace('\\', '/');
                TarArchiveEntry entry = new TarArchiveEntry(file.toFile(), entryName);
                taos.putArchiveEntry(entry);

                try (InputStream is = Files.newInputStream(file)) {
                    IOUtils.copy(is, taos);
                }

                taos.closeArchiveEntry();
                log.debug("已添加文件到TAR压缩包: {}", entryName);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                if (!dir.equals(baseDir)) {
                    String entryName = baseDir.relativize(dir).toString().replace('\\', '/') + "/";
                    TarArchiveEntry entry = new TarArchiveEntry(entryName);
                    entry.setMode(TarArchiveEntry.DEFAULT_DIR_MODE);
                    taos.putArchiveEntry(entry);
                    taos.closeArchiveEntry();
                    log.debug("已添加目录到TAR压缩包: {}", entryName);
                }
                return FileVisitResult.CONTINUE;
            }
        });
    }

    // ==================== EXTRACTION METHODS ====================

    private static void extractTarGz(File tarGzFile, Path extractDir) throws IOException {
        try (FileInputStream fis = new FileInputStream(tarGzFile);
             GzipCompressorInputStream gzis = new GzipCompressorInputStream(fis);
             TarArchiveInputStream tais = new TarArchiveInputStream(gzis)) {

            extractTarArchive(tais, extractDir);
        }
    }

    private static void extractTar(File tarFile, Path extractDir) throws IOException {
        try (FileInputStream fis = new FileInputStream(tarFile);
             TarArchiveInputStream tais = new TarArchiveInputStream(fis)) {

            extractTarArchive(tais, extractDir);
        }
    }

    private static void extractZip(File zipFile, Path extractDir) throws IOException {
        try (FileInputStream fis = new FileInputStream(zipFile);
             ZipInputStream zis = new ZipInputStream(fis)) {

            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                Path entryPath = extractDir.resolve(entry.getName());

                // Security check to prevent directory traversal
                if (!entryPath.normalize().startsWith(extractDir.normalize())) {
                    throw new IOException("Entry is outside of the target directory: " + entry.getName());
                }

                if (entry.isDirectory()) {
                    Files.createDirectories(entryPath);
                    log.debug("已创建目录: {}", entryPath);
                } else {
                    Files.createDirectories(entryPath.getParent());
                    try (OutputStream os = Files.newOutputStream(entryPath)) {
                        IOUtils.copy(zis, os);
                    }
                    log.debug("已解压文件: {}", entryPath);
                }
                zis.closeEntry();
            }
        }
    }

    private static void extractTarArchive(TarArchiveInputStream tais, Path extractDir) throws IOException {
        TarArchiveEntry entry;
        while ((entry = tais.getNextTarEntry()) != null) {
            Path entryPath = extractDir.resolve(entry.getName());

            // Security check to prevent directory traversal
            if (!entryPath.normalize().startsWith(extractDir.normalize())) {
                throw new IOException("Entry is outside of the target directory: " + entry.getName());
            }

            if (entry.isDirectory()) {
                Files.createDirectories(entryPath);
                log.debug("已创建目录: {}", entryPath);
            } else {
                Files.createDirectories(entryPath.getParent());
                try (OutputStream os = Files.newOutputStream(entryPath)) {
                    IOUtils.copy(tais, os);
                }
                log.debug("已解压文件: {}", entryPath);
            }
        }
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Ensure parent directory exists for the given file
     */
    private static void ensureParentDirectoryExists(File file) throws IOException {
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                throw new IOException("无法创建输出目录: " + parentDir.getAbsolutePath());
            }
        }
    }

    /**
     * Get file count in an archive (utility method for validation)
     */
    public static int getArchiveFileCount(String archiveFileName) throws IOException {
        ArchiveFormat format = ArchiveFormat.fromFileName(archiveFileName);
        return getArchiveFileCount(archiveFileName, format);
    }

    /**
     * Get file count in an archive with explicit format
     */
    public static int getArchiveFileCount(String archiveFileName, ArchiveFormat format) throws IOException {
        File archiveFile = new File(archiveFileName);
        if (!archiveFile.exists() || !archiveFile.isFile()) {
            throw new IllegalArgumentException("压缩文件不存在: " + archiveFileName);
        }

        int count = 0;
        switch (format) {
            case TAR_GZ:
                try (FileInputStream fis = new FileInputStream(archiveFile);
                     GzipCompressorInputStream gzis = new GzipCompressorInputStream(fis);
                     TarArchiveInputStream tais = new TarArchiveInputStream(gzis)) {
                    while (tais.getNextTarEntry() != null) {
                        count++;
                    }
                }
                break;
            case TAR:
                try (FileInputStream fis = new FileInputStream(archiveFile);
                     TarArchiveInputStream tais = new TarArchiveInputStream(fis)) {
                    while (tais.getNextTarEntry() != null) {
                        count++;
                    }
                }
                break;
            case ZIP:
                try (FileInputStream fis = new FileInputStream(archiveFile);
                     ZipInputStream zis = new ZipInputStream(fis)) {
                    while (zis.getNextEntry() != null) {
                        count++;
                        zis.closeEntry();
                    }
                }
                break;
            default:
                throw new IllegalArgumentException("Unsupported archive format: " + format);
        }

        return count;
    }

    /**
     * Check if a file is a supported archive format
     */
    public static boolean isSupportedArchive(String fileName) {
        try {
            ArchiveFormat.fromFileName(fileName);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
