package com.hll.dataconvertservice.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import org.springframework.util.StringUtils;

@Configuration
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "aws.s3")
@Data
public class S3Config {
    
    private String bucketName;
    private String region;
    private String accessKeyId;
    private String secretAccessKey;
    private boolean useIamRole;
    
    @Bean
    public S3Client s3Client() {
        AwsCredentialsProvider credentialsProvider;

        if (useIamRole) {
            // Explicitly use IAM role or default credentials chain
            credentialsProvider = DefaultCredentialsProvider.create();
        } else if (StringUtils.hasText(accessKeyId) && StringUtils.hasText(secretAccessKey)) {
            // Use explicit access keys when both are provided
            AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey);
            credentialsProvider = StaticCredentialsProvider.create(awsCredentials);
        } else {
            // Fallback to default credentials chain when access keys are not provided
            credentialsProvider = DefaultCredentialsProvider.create();
        }

        return S3Client.builder()
                .region(Region.of(region))
                .credentialsProvider(credentialsProvider)
                .build();
    }
}
