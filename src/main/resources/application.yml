server:
  port: 10091
logging:
  level:
#    root: debug
    com.hll.dataconvertservice: debug
pg:
  url: 10.194.18.179
  port: 15999
  user: postgres
  pwd: <PERSON><PERSON><PERSON>@2021

# AWS S3 Configuration
aws:
  s3:
    bucket-name: ${AWS_S3_BUCKET_NAME:sg-flexgrid-s3-prd}
    region: ${AWS_S3_REGION:ap-southeast-1}
    access-key-id: ${AWS_ACCESS_KEY_ID:********************}
    secret-access-key: ${AWS_SECRET_ACCESS_KEY:kfEc8afpzcnlbt+j3qp6jSk3uyiXiDsKa7yEJ1XM}
    # Optional: Use IAM roles instead of access keys when running on EC2
    use-iam-role: ${AWS_USE_IAM_ROLE:false}