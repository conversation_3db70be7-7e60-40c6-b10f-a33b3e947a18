# S3 Upload Configuration Guide

This document explains how to configure AWS S3 integration for the Data Convert Service.

## Overview

The `incrementalDataConvert2GeoJSON` endpoint now automatically uploads generated GeoJSON files to an AWS S3 bucket after successful conversion. The S3 upload is executed only when the GeoJSON conversion is successful (`res = true`).

## Configuration

### 1. Environment Variables (Recommended)

Set the following environment variables:

```bash
export AWS_S3_BUCKET_NAME=your-bucket-name
export AWS_S3_REGION=us-east-1
export AWS_ACCESS_KEY_ID=your-access-key
export AWS_SECRET_ACCESS_KEY=your-secret-key
export AWS_USE_IAM_ROLE=false
```

### 2. Application Properties

Alternatively, you can configure in `application.yml`:

```yaml
aws:
  s3:
    bucket-name: your-bucket-name
    region: us-east-1
    access-key-id: your-access-key
    secret-access-key: your-secret-key
    use-iam-role: false
```

### 3. IAM Role (Recommended for EC2/ECS)

When running on AWS infrastructure, use IAM roles instead of access keys:

```yaml
aws:
  s3:
    bucket-name: your-bucket-name
    region: us-east-1
    use-iam-role: true
    # access-key-id and secret-access-key are ignored when use-iam-role is true
```

## Credential Selection Logic

The system selects credentials in the following priority:

1. **If `use-iam-role: true`** → Uses IAM role or AWS default credentials chain
2. **If `use-iam-role: false` AND both `access-key-id` and `secret-access-key` are provided** → Uses explicit credentials
3. **If `use-iam-role: false` BUT credentials are missing** → Falls back to AWS default credentials chain

## S3 Bucket Structure

Files are uploaded with the following structure:
```
geojson-exports/
├── 2024/01/15/143022/
│   ├── link_m-node_m/
│   │   ├── file1.geojson
│   │   └── file2.geojson
│   └── relation_m/
│       └── file3.geojson
```

- `2024/01/15/143022/` - Date and time of export (yyyy/MM/dd/HHmmss)
- `link_m-node_m/` - Table names joined with hyphens
- Files maintain their original names from the local directory

## Required IAM Permissions

Your AWS user or role needs the following S3 permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:PutObject",
                "s3:PutObjectAcl"
            ],
            "Resource": "arn:aws:s3:::your-bucket-name/*"
        }
    ]
}
```

## Behavior

1. **Success Flow**: GeoJSON conversion succeeds → Files uploaded to S3 → Success response includes upload count
2. **Partial Failure**: GeoJSON conversion succeeds → S3 upload fails → Success response (S3 failure logged but doesn't affect overall success)
3. **Complete Failure**: GeoJSON conversion fails → No S3 upload attempted → Error response

## Logging

The service provides detailed logging for S3 operations:
- Upload start/completion
- Individual file upload success/failure
- Upload statistics in final success message

## Content Types

Files are uploaded with appropriate content types:
- `.geojson`, `.json` → `application/geo+json`
- `.txt` → `text/plain`
- `.csv` → `text/csv`
- `.xml` → `application/xml`
- Others → `application/octet-stream`
